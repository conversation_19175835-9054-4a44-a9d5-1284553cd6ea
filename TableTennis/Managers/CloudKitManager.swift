import Foundation
import CloudKit
import SwiftUI

/// Manager voor CloudKit operaties
class CloudKitManager: ObservableObject {
    static let shared = CloudKitManager()
    
    private let container = CKContainer(identifier: "iCloud.nl.joostlaurman.tabletennis")

    /// Altijd de public database gebruiken voor consistentie
    private var database: CKDatabase {
        return container.publicCloudDatabase
    }
    
    @Published var currentCompetition: Competition? {
        didSet {
            // Sla de geselecteerde competitie op in UserDefaults
            if let competition = currentCompetition {
                if let data = try? JSONEncoder().encode(competition) {
                    UserDefaults.standard.set(data, forKey: "selectedCompetition")
                }
            } else {
                UserDefaults.standard.removeObject(forKey: "selectedCompetition")
            }
        }
    }
    @Published var isSignedInToiCloud = false {
        didSet {
            if isSignedInToiCloud && !oldValue {
                // iCloud status is zojuist actief geworden
                print("🔐 iCloud status became active")
            }
        }
    }
    @Published var cloudKitError: String?
    
    private init() {
        loadSavedCompetition()
        checkiCloudStatus()
    }

    // MARK: - Saved Competition Management

    private func loadSavedCompetition() {
        print("💾 Loading saved competition from UserDefaults...")
        guard let data = UserDefaults.standard.data(forKey: "selectedCompetition"),
              let competition = try? JSONDecoder().decode(Competition.self, from: data) else {
            print("💾 No saved competition found")
            return
        }

        print("💾 Found saved competition: \(competition.name)")
        // Stel de competitie in
        currentCompetition = competition
        print("💾 Saved competition loaded and public database will be used")

        // Start schema initialisatie en migratie op de achtergrond
        Task {
            do {
                try await initializeCloudKitSchema()
                try await migrateToPublicDatabase()
            } catch {
                print("⚠️ Schema initialization or migration failed: \(error)")
            }
        }
    }

    func clearSavedCompetition() {
        UserDefaults.standard.removeObject(forKey: "selectedCompetition")
        currentCompetition = nil
    }

    // MARK: - iCloud Status
    
    /// Controleert of gebruiker is ingelogd op iCloud
    func checkiCloudStatus() {
        print("🔍 Checking iCloud status...")
        container.accountStatus { [weak self] status, error in
            DispatchQueue.main.async {
                print("📱 iCloud account status: \(status)")
                if let error = error {
                    print("❌ Error checking iCloud status: \(error)")
                }

                switch status {
                case .available:
                    print("✅ iCloud account available")
                    self?.isSignedInToiCloud = true
                    self?.cloudKitError = nil
                case .noAccount:
                    print("❌ No iCloud account found")
                    self?.isSignedInToiCloud = false
                    self?.cloudKitError = "Geen iCloud account gevonden. Log in op iCloud in Instellingen."
                case .restricted:
                    print("❌ iCloud account restricted")
                    self?.isSignedInToiCloud = false
                    self?.cloudKitError = "iCloud account is beperkt."
                case .couldNotDetermine:
                    print("❌ Could not determine iCloud status")
                    self?.isSignedInToiCloud = false
                    self?.cloudKitError = "Kan iCloud status niet bepalen."
                case .temporarilyUnavailable:
                    print("⏳ iCloud temporarily unavailable")
                    self?.isSignedInToiCloud = false
                    self?.cloudKitError = "iCloud is tijdelijk niet beschikbaar."
                @unknown default:
                    print("❓ Unknown iCloud status")
                    self?.isSignedInToiCloud = false
                    self?.cloudKitError = "Onbekende iCloud status."
                }
            }
        }
    }
    
    // MARK: - Competition Management
    
    /// Haalt alle beschikbare competities op
    func fetchAvailableCompetitions() async throws -> [Competition] {
        let publicDatabase = container.publicCloudDatabase

        print("🔍 Fetching competitions from CloudKit...")

        // Probeer een alternatieve aanpak: gebruik records(for:) in plaats van query
        // Dit vermijdt mogelijke queryable field problemen
        do {
            // Eerst proberen we een eenvoudige query zonder predicates
            let query = CKQuery(recordType: Competition.recordType, predicate: NSPredicate(value: true))

            // Expliciet geen sort descriptors instellen
            query.sortDescriptors = nil

            let result = try await publicDatabase.records(matching: query)
            var competitions: [Competition] = []

            print("📊 Found \(result.matchResults.count) competition records")

            for (recordID, recordResult) in result.matchResults {
                switch recordResult {
                case .success(let record):
                    print("✅ Successfully fetched record: \(recordID)")
                    print("📋 Record fields: \(record.allKeys())")
                    if let competition = Competition(from: record) {
                        print("✅ Successfully converted to Competition: \(competition.name)")
                        print("🔍 Competition details: isActive=\(competition.isActive), createdBy=\(competition.createdBy)")
                        // Filter alleen actieve competities
                        if competition.isActive {
                            competitions.append(competition)
                            print("✅ Added active competition: \(competition.name)")
                        } else {
                            print("⚠️ Skipped inactive competition: \(competition.name)")
                        }
                    } else {
                        print("❌ Failed to convert record to Competition")
                        print("❌ Record data: \(record)")
                    }
                case .failure(let error):
                    print("❌ Error fetching competition record \(recordID): \(error)")
                }
            }

            print("🏆 Returning \(competitions.count) active competitions")

            // Debug: Log alle competitie namen
            if competitions.isEmpty {
                print("⚠️ No active competitions found!")
            } else {
                print("📋 Active competitions:")
                for comp in competitions {
                    print("  - \(comp.name) (created: \(comp.createdAt), by: \(comp.createdBy))")
                }
            }

            return competitions.sorted { $0.createdAt > $1.createdAt }
        } catch {
            print("❌ Error in fetchAvailableCompetitions: \(error)")
            throw error
        }
    }
    
    /// Maakt een nieuwe competitie aan
    func createCompetition(name: String, description: String) async throws -> Competition {
        guard let userID = try await getCurrentUserID() else {
            throw CloudKitError.userNotFound
        }
        
        let competition = Competition(name: name, description: description, createdBy: userID)
        let record = competition.toCKRecord()
        
        let publicDatabase = container.publicCloudDatabase
        let savedRecord = try await publicDatabase.save(record)
        
        guard let savedCompetition = Competition(from: savedRecord) else {
            throw CloudKitError.recordConversionFailed
        }
        
        // Maak een shared database voor deze competitie
        try await createSharedDatabase(for: savedCompetition)
        
        return savedCompetition
    }
    
    /// Maakt een shared database voor een competitie
    private func createSharedDatabase(for competition: Competition) async throws {
        // Voor nu gebruiken we de public database
        // In een productie app zou je hier een CKShare maken
        print("Created shared database for competition: \(competition.name)")
    }
    
    /// Selecteert een competitie en stelt de shared database in
    func selectCompetition(_ competition: Competition) {
        print("🏆 Selecting competition: \(competition.name)")
        currentCompetition = competition

        // Sla de geselecteerde competitie op in UserDefaults
        if let data = try? JSONEncoder().encode(competition) {
            UserDefaults.standard.set(data, forKey: "selectedCompetition")
            print("💾 Competition saved to UserDefaults")
        } else {
            print("❌ Failed to save competition to UserDefaults")
        }

        print("🔗 Public database will be used for all operations")

        // Start schema initialisatie en migratie op de achtergrond
        Task {
            do {
                try await initializeCloudKitSchema()
                try await migrateToPublicDatabase()
            } catch {
                print("⚠️ Schema initialization or migration failed: \(error)")
            }
        }
    }

    /// Controleert of de huidige gebruiker de eigenaar is van de competitie
    func isCurrentUserOwner(of competition: Competition) async -> Bool {
        guard let currentUserId = try? await getCurrentUserID() else {
            return false
        }
        return competition.createdBy == currentUserId
    }

    /// Controleert of de huidige gebruiker toegang heeft tot de competitie
    func hasAccessToCompetition(_ competition: Competition) async -> Bool {
        // Eigenaar heeft altijd toegang
        if await isCurrentUserOwner(of: competition) {
            return true
        }

        // Controleer of er een goedgekeurde join request bestaat (response-based systeem)
        guard let currentUserId = try? await getCurrentUserID() else {
            return false
        }

        do {
            // Haal de join request op
            guard let joinRequest = try await getJoinRequest(
                competitionId: competition.id,
                requesterId: currentUserId
            ) else {
                return false
            }

            // Haal de responses op voor deze join request
            let responses = try await getJoinRequestResponses(for: [joinRequest.id])

            // Controleer of er een approved response is
            return responses.contains { $0.decision == .approved }
        } catch {
            print("❌ Error checking access to competition: \(error)")
            return false
        }
    }
    
    // MARK: - User Management
    
    /// Haalt de huidige gebruiker ID op
    private func getCurrentUserID() async throws -> String? {
        let userRecordID = try await container.userRecordID()
        return userRecordID.recordName
    }

    /// Haalt de huidige gebruikersnaam op uit CloudKit
    func getCurrentUserName() async throws -> String {
        do {
            let userRecordID = try await container.userRecordID()

            // Probeer de user record op te halen voor meer informatie
            let userRecord = try await container.publicCloudDatabase.record(for: userRecordID)

            // Probeer verschillende velden voor de naam
            if let firstName = userRecord["firstName"] as? String,
               let lastName = userRecord["lastName"] as? String {
                return "\(firstName) \(lastName)"
            } else if let displayName = userRecord["displayName"] as? String {
                return displayName
            } else if let name = userRecord["name"] as? String {
                return name
            }
        } catch {
            print("⚠️ Could not fetch user record details: \(error)")
        }

        // Fallback: gebruik de eerste 8 karakters van de user ID
        do {
            let userRecordID = try await container.userRecordID()
            let userID = userRecordID.recordName
            let shortID = String(userID.prefix(8))
            return "Gebruiker \(shortID)"
        } catch {
            print("❌ Could not get user ID: \(error)")
            return "Onbekende Gebruiker"
        }
    }
    
    // MARK: - Data Operations
    
    /// Haalt alle spelers voor de huidige competitie op
    func fetchPlayers() async throws -> [Player] {
        guard let competition = currentCompetition else {
            print("❌ fetchPlayers: No competition selected")
            throw CloudKitError.noCompetitionSelected
        }

        print("👥 Fetching players for competition: \(competition.name) (ID: \(competition.id.uuidString))")

        let predicate = NSPredicate(format: "competitionId == %@", competition.id.uuidString)
        let query = CKQuery(recordType: Player.recordType, predicate: predicate)

        let result = try await database.records(matching: query)
        var players: [Player] = []

        print("📊 Found \(result.matchResults.count) player records")

        for (recordID, recordResult) in result.matchResults {
            switch recordResult {
            case .success(let record):
                print("✅ Successfully fetched player record: \(recordID)")
                if let player = Player(from: record) {
                    print("✅ Successfully converted to Player: \(player.name)")
                    players.append(player)
                } else {
                    print("❌ Failed to convert record to Player")
                }
            case .failure(let error):
                print("❌ Error fetching player record \(recordID): \(error)")
            }
        }

        print("👥 Returning \(players.count) players")
        return players.sorted { $0.name < $1.name }
    }
    
    /// Slaat een speler op met retry logic voor concurrency conflicts
    func savePlayer(_ player: Player, maxRetries: Int = 3) async throws {
        print("💾 CloudKitManager: Attempting to save player \(player.name)")
        print("📋 CloudKitManager: Using public database for competition: \(currentCompetition?.name ?? "unknown")")

        var lastError: Error?

        for attempt in 1...maxRetries {
            do {
                try await savePlayerInternal(player)
                print("✅ CloudKitManager: Successfully saved player \(player.name) on attempt \(attempt)")
                return
            } catch let error as CKError where error.code == .serverRecordChanged {
                print("⚠️ CloudKitManager: Server record changed for \(player.name), attempt \(attempt)/\(maxRetries)")
                lastError = error

                if attempt < maxRetries {
                    // Wacht een korte tijd voordat we opnieuw proberen
                    try await Task.sleep(nanoseconds: UInt64(attempt * 500_000_000)) // 0.5s, 1s, 1.5s
                    continue
                }
            } catch let error as CKError where error.code == .permissionFailure {
                print("⚠️ CloudKitManager: Permission failure for player \(player.name) - player owned by another user, skipping save")
                return // Skip deze speler, geen error throwen
            } catch {
                print("❌ CloudKitManager: Error saving player \(player.name): \(error)")
                throw error
            }
        }

        // Als we hier komen, hebben alle retry pogingen gefaald
        if let lastError = lastError {
            print("❌ CloudKitManager: Failed to save player \(player.name) after \(maxRetries) attempts")
            throw lastError
        }
    }

    /// Interne methode voor het opslaan van een speler
    private func savePlayerInternal(_ player: Player) async throws {
        // Smart record management: zoek eerst naar bestaande records voor deze speler
        print("🔍 CloudKitManager: Checking for existing player records...")

        // Probeer eerst het bestaande record op te halen op basis van ID
        let recordID = CKRecord.ID(recordName: "Player_\(player.id.uuidString)")

        do {
            // Probeer bestaand record op te halen
            print("🔍 CloudKitManager: Checking for existing player record for \(player.name) with ID \(player.id)")
            let existingRecord = try await database.record(for: recordID)
            print("📝 CloudKitManager: Found existing record, updating player \(player.name)")

            // Update het bestaande record
            let updatedRecord = player.toCKRecord(existingRecord: existingRecord)
            _ = try await database.save(updatedRecord)
            print("✅ CloudKitManager: Successfully updated existing player record for \(player.name)")
            return

        } catch let error as CKError where error.code == .unknownItem {
            // Record bestaat niet, ga door naar het aanmaken van een nieuw record
            print("🔄 CloudKitManager: No existing record found for player \(player.name), will create new one")
        } catch {
            // Andere fout bij ophalen van bestaand record
            print("⚠️ CloudKitManager: Error fetching existing record for player \(player.name): \(error)")
            throw error
        }

        // Maak een nieuw record aan
        print("🔄 CloudKitManager: Creating new player record for \(player.name)...")
        let newRecord = player.toCKRecord()

        _ = try await database.save(newRecord)
        print("✅ CloudKitManager: Successfully created new player record for \(player.name)")
    }

    /// Slaat meerdere spelers op in batch met sequential processing om concurrency conflicts te voorkomen
    func savePlayersSequentially(_ players: [Player]) async throws {
        print("💾 CloudKitManager: Starting sequential save of \(players.count) players...")

        // Controleer eerst of we toestemming hebben om te schrijven
        guard let competition = currentCompetition else {
            print("❌ CloudKitManager: No competition selected for sequential save")
            throw CloudKitError.noCompetitionSelected
        }

        let isOwner = await isCurrentUserOwner(of: competition)
        if !isOwner {
            print("⚠️ CloudKitManager: User is not owner of competition, some saves may fail")
        }

        var savedCount = 0
        var failedPlayers: [(Player, Error)] = []
        var permissionFailures: [Player] = []

        for player in players {
            do {
                try await savePlayer(player)
                savedCount += 1
                print("✅ CloudKitManager: Saved player \(savedCount)/\(players.count): \(player.name)")
            } catch let error as CKError where error.code == .permissionFailure {
                print("⚠️ CloudKitManager: Permission failure for player \(player.name) - skipping")
                permissionFailures.append(player)
            } catch {
                print("❌ CloudKitManager: Failed to save player \(player.name): \(error)")
                failedPlayers.append((player, error))
            }
        }

        print("💾 CloudKitManager: Sequential save completed - \(savedCount) succeeded, \(failedPlayers.count) failed, \(permissionFailures.count) permission denied")

        if !failedPlayers.isEmpty {
            print("❌ CloudKitManager: Failed players:")
            for (player, error) in failedPlayers {
                print("❌   \(player.name): \(error.localizedDescription)")
            }
        }

        if !permissionFailures.isEmpty {
            print("⚠️ CloudKitManager: Permission denied for players (owned by other users):")
            for player in permissionFailures {
                print("⚠️   \(player.name): Statistics updated locally only")
            }
            print("ℹ️ This is normal in shared competitions - these players were created by other users")
        }

        // Alleen echte fouten throwen, permission failures zijn verwacht in shared competitions
        if !failedPlayers.isEmpty {
            let firstError = failedPlayers.first!.1
            print("❌ CloudKitManager: \(failedPlayers.count) players failed to save with non-permission errors")
            throw firstError
        }

        print("✅ CloudKitManager: All players processed successfully (some may be local-only due to permissions)")
    }
    
    /// Haalt alle wedstrijden voor de huidige competitie op
    func fetchMatches() async throws -> [Match] {
        guard let competition = currentCompetition else {
            print("❌ fetchMatches: No competition selected")
            throw CloudKitError.noCompetitionSelected
        }

        print("🏓 Fetching matches for competition: \(competition.name) (ID: \(competition.id.uuidString))")

        let predicate = NSPredicate(format: "competitionId == %@", competition.id.uuidString)
        let query = CKQuery(recordType: Match.recordType, predicate: predicate)

        let result = try await database.records(matching: query)
        var matches: [Match] = []

        print("📊 Found \(result.matchResults.count) match records")

        for (recordID, recordResult) in result.matchResults {
            switch recordResult {
            case .success(let record):
                print("✅ Successfully fetched match record: \(recordID)")
                print("🎮 DEBUG: CloudKit record has games field: \(record["games"] != nil)")
                if let gamesData = record["games"] as? Data {
                    print("🎮 DEBUG: Games data size: \(gamesData.count) bytes")
                }
                if let match = Match(from: record) {
                    print("✅ Successfully converted to Match: \(match.description)")
                    print("🎮 DEBUG: Match has \(match.games.count) games after conversion")
                    matches.append(match)
                } else {
                    print("❌ Failed to convert record to Match")
                }
            case .failure(let error):
                print("❌ Error fetching match record \(recordID): \(error)")
            }
        }

        print("🏓 Returning \(matches.count) matches")
        return matches.sorted { $0.createdAt > $1.createdAt }
    }
    
    /// Slaat een wedstrijd op
    func saveMatch(_ match: Match) async throws {
        print("💾 CloudKitManager: Attempting to save match \(match.id)")
        print("📋 CloudKitManager: Using public database for competition: \(currentCompetition?.name ?? "unknown")")

        // Smart record management: zoek eerst naar bestaande records voor deze match
        print("🔍 CloudKitManager: Checking for existing match records...")

        do {
            // Probeer eerst het bestaande record op te halen
            let recordID = CKRecord.ID(recordName: "Match_\(match.id.uuidString)")

            do {
                // Probeer bestaand record op te halen
                print("🔍 CloudKitManager: Checking for existing record for match \(match.id)...")
                let existingRecord = try await database.record(for: recordID)
                print("📝 CloudKitManager: Found existing record, updating match \(match.id)...")

                // Update het bestaande record
                let updatedRecord = match.toCKRecord(existingRecord: existingRecord)
                _ = try await database.save(updatedRecord)
                print("✅ CloudKitManager: Successfully updated existing match record for \(match.id)")

            } catch let error as CKError where error.code == .unknownItem {
                // Record bestaat niet, maak een nieuw aan
                print("🔄 CloudKitManager: No existing record found, creating new match record for \(match.id)...")
                let newRecord = match.toCKRecord()
                _ = try await database.save(newRecord)
                print("✅ CloudKitManager: Successfully created new match record for \(match.id)")

            } catch {
                // Andere fout bij ophalen van bestaand record
                print("⚠️ CloudKitManager: Error fetching existing record for match \(match.id): \(error)")
                throw error
            }

        } catch {
            print("❌ CloudKitManager: Error saving match \(match.id): \(error)")
            throw error
        }
    }
    
    /// Verwijdert een wedstrijd
    func deleteMatch(_ match: Match) async throws {
        print("🗑️ CloudKitManager: Attempting to delete match \(match.id)")
        print("📋 CloudKitManager: Using public database for deletion")

        let recordID = CKRecord.ID(recordName: "Match_\(match.id.uuidString)")
        _ = try await database.deleteRecord(withID: recordID)
    }

    // MARK: - Schema Initialization

    /// Initialiseert het CloudKit schema door dummy records aan te maken
    func initializeCloudKitSchema() async throws {
        print("🔧 Initializing CloudKit schema...")

        do {
            // Probeer een dummy Player record aan te maken om het schema te forceren
            let dummyPlayerRecord = CKRecord(recordType: Player.recordType)
            dummyPlayerRecord["name"] = "SCHEMA_INIT_DUMMY"
            dummyPlayerRecord["eloRating"] = 1200.0
            dummyPlayerRecord["competitionId"] = "dummy"
            dummyPlayerRecord["createdAt"] = Date()
            dummyPlayerRecord["matchesPlayed"] = 0
            dummyPlayerRecord["matchesWon"] = 0
            dummyPlayerRecord["gamesPlayed"] = 0
            dummyPlayerRecord["gamesWon"] = 0

            _ = try await database.save(dummyPlayerRecord)
            print("✅ Player schema initialized")

            // Verwijder het dummy record weer
            try await database.deleteRecord(withID: dummyPlayerRecord.recordID)
            print("🗑️ Dummy player record cleaned up")

        } catch let error as CKError where error.code == .unknownItem {
            print("⚠️ CloudKit schema not configured. Please configure record types in CloudKit Console.")
            print("📋 Required record types: Player, Match, Competition, CompetitionJoinRequest")
            throw CloudKitError.schemaNotConfigured
        } catch {
            print("⚠️ Schema initialization failed: \(error)")
        }

        do {
            // Probeer een dummy Match record aan te maken met de juiste velden
            let dummyMatchRecord = CKRecord(recordType: Match.recordType)

            // Maak dummy player data
            let dummyPlayer = Player(name: "SCHEMA_DUMMY", competitionId: UUID(), eloRating: 1200.0)
            if let playerData = try? JSONEncoder().encode(dummyPlayer) {
                dummyMatchRecord["team1Player1"] = playerData
                dummyMatchRecord["team2Player1"] = playerData
            }

            dummyMatchRecord["type"] = "singles"
            dummyMatchRecord["status"] = "notStarted"
            dummyMatchRecord["bestOfGames"] = 3
            dummyMatchRecord["createdAt"] = Date()
            dummyMatchRecord["competitionId"] = "dummy"

            // Voeg lege games array toe
            if let gamesData = try? JSONEncoder().encode([Game]()) {
                dummyMatchRecord["games"] = gamesData
            }

            _ = try await database.save(dummyMatchRecord)
            print("✅ Match schema initialized")

            // Verwijder het dummy record weer
            try await database.deleteRecord(withID: dummyMatchRecord.recordID)
            print("🗑️ Dummy match record cleaned up")

        } catch let error as CKError where error.code == .unknownItem {
            print("⚠️ Match record type not configured in CloudKit Console")
        } catch {
            print("⚠️ Match schema initialization failed: \(error)")
        }

        print("✅ CloudKit schema initialization completed")
    }

    // MARK: - Database Migration

    /// Migreert alle data van shared database naar public database
    func migrateToPublicDatabase() async throws {
        print("🔄 Starting migration from shared database to public database...")

        guard let competition = currentCompetition else {
            print("❌ No competition selected for migration")
            throw CloudKitError.noCompetitionSelected
        }

        // Check if migration is needed by trying to find records in public database
        let publicDatabase = container.publicCloudDatabase
        let sharedDatabase = container.sharedCloudDatabase

        // Try to find players in public database
        let publicPlayersQuery = CKQuery(recordType: Player.recordType,
                                       predicate: NSPredicate(format: "competitionId == %@", competition.id.uuidString))
        let publicPlayersResult = try await publicDatabase.records(matching: publicPlayersQuery)

        if !publicPlayersResult.matchResults.isEmpty {
            print("✅ Records already exist in public database, migration not needed")
            return
        }

        print("🔄 No records found in public database, starting migration...")

        // Migrate players
        try await migratePlayersToPublicDatabase(from: sharedDatabase, to: publicDatabase, competition: competition)

        // Migrate matches
        try await migrateMatchesToPublicDatabase(from: sharedDatabase, to: publicDatabase, competition: competition)

        print("✅ Migration completed successfully!")
    }

    private func migratePlayersToPublicDatabase(from sharedDB: CKDatabase, to publicDB: CKDatabase, competition: Competition) async throws {
        print("🔄 Migrating players...")

        let query = CKQuery(recordType: Player.recordType,
                           predicate: NSPredicate(format: "competitionId == %@", competition.id.uuidString))

        let result = try await sharedDB.records(matching: query)
        var migratedCount = 0

        for (_, result) in result.matchResults {
            switch result {
            case .success(let record):
                if let player = Player(from: record) {
                    // Create new record in public database
                    let newRecord = player.toCKRecord()
                    _ = try await publicDB.save(newRecord)
                    migratedCount += 1
                    print("✅ Migrated player: \(player.name)")
                }
            case .failure(let error):
                print("❌ Failed to fetch player record: \(error)")
            }
        }

        print("✅ Migrated \(migratedCount) players to public database")
    }

    private func migrateMatchesToPublicDatabase(from sharedDB: CKDatabase, to publicDB: CKDatabase, competition: Competition) async throws {
        print("🔄 Migrating matches...")

        let query = CKQuery(recordType: Match.recordType,
                           predicate: NSPredicate(format: "competitionId == %@", competition.id.uuidString))

        let result = try await sharedDB.records(matching: query)
        var migratedCount = 0

        for (_, result) in result.matchResults {
            switch result {
            case .success(let record):
                if let match = Match(from: record) {
                    // Create new record in public database
                    let newRecord = match.toCKRecord()
                    _ = try await publicDB.save(newRecord)
                    migratedCount += 1
                    print("✅ Migrated match: \(match.team1Player1.name) vs \(match.team2Player1.name)")
                }
            case .failure(let error):
                print("❌ Failed to fetch match record: \(error)")
            }
        }

        print("✅ Migrated \(migratedCount) matches to public database")
    }

    // MARK: - Competition Join Requests

    /// Dient een aanvraag in om deel te nemen aan een competitie
    func requestToJoinCompetition(_ competition: Competition, requesterName: String) async throws {
        guard let currentUserId = try await getCurrentUserID() else {
            throw CloudKitError.userNotFound
        }

        // Controleer of er al een aanvraag bestaat
        do {
            if let existingRequest = try await getJoinRequest(competitionId: competition.id, requesterId: currentUserId) {
                if existingRequest.isPending {
                    throw CloudKitError.joinRequestAlreadyExists
                } else if existingRequest.isApproved {
                    throw CloudKitError.alreadyJoined
                }
                // Als de vorige aanvraag is afgewezen, kunnen we een nieuwe indienen
            }
        } catch {
            // Als het record type niet bestaat, maak het schema aan
            if let ckError = error as? CKError, ckError.code == .unknownItem {
                print("⚠️ CompetitionJoinRequest record type not found, creating schema...")
                try await createJoinRequestSchema()
            } else {
                throw error
            }
        }

        let joinRequest = CompetitionJoinRequest(
            competitionId: competition.id,
            requesterId: currentUserId,
            requesterName: requesterName
        )

        let database = container.publicCloudDatabase
        let record = joinRequest.toCKRecord()
        _ = try await database.save(record)

        print("✅ Join request submitted for competition: \(competition.name)")
    }

    /// Haalt een specifieke join request op
    func getJoinRequest(competitionId: UUID, requesterId: String) async throws -> CompetitionJoinRequest? {
        let database = container.publicCloudDatabase

        let predicate = NSPredicate(format: "competitionId == %@ AND requesterId == %@",
                                   competitionId.uuidString, requesterId)
        let query = CKQuery(recordType: CompetitionJoinRequest.recordType, predicate: predicate)

        do {
            let result = try await database.records(matching: query)

            for (_, recordResult) in result.matchResults {
                switch recordResult {
                case .success(let record):
                    if let joinRequest = CompetitionJoinRequest(from: record) {
                        return joinRequest
                    }
                case .failure(let error):
                    print("❌ Error fetching join request: \(error)")
                }
            }

            return nil
        } catch {
            // Als het record type niet bestaat, return nil (geen bestaande request)
            if let ckError = error as? CKError, ckError.code == .unknownItem {
                print("⚠️ CompetitionJoinRequest record type not found")
                return nil
            }
            throw error
        }
    }

    /// Haalt alle join requests op voor een competitie (alleen voor eigenaar)
    func getJoinRequestsForCompetition(_ competition: Competition) async throws -> [CompetitionJoinRequest] {
        guard await isCurrentUserOwner(of: competition) else {
            throw CloudKitError.notAuthorized
        }

        let database = container.publicCloudDatabase
        let predicate = NSPredicate(format: "competitionId == %@", competition.id.uuidString)
        let query = CKQuery(recordType: CompetitionJoinRequest.recordType, predicate: predicate)

        do {
            let result = try await database.records(matching: query)
            var joinRequests: [CompetitionJoinRequest] = []

            for (_, recordResult) in result.matchResults {
                switch recordResult {
                case .success(let record):
                    if let joinRequest = CompetitionJoinRequest(from: record) {
                        joinRequests.append(joinRequest)
                    }
                case .failure(let error):
                    print("❌ Error fetching join request: \(error)")
                }
            }

            return joinRequests.sorted { $0.requestedAt > $1.requestedAt }
        } catch {
            // Als het record type niet bestaat, probeer het aan te maken
            if let ckError = error as? CKError, ckError.code == .unknownItem {
                print("⚠️ CompetitionJoinRequest record type not found, creating schema...")
                try await createJoinRequestSchema()
                // Return empty array voor nu - gebruiker kan opnieuw proberen
                return []
            }
            throw error
        }
    }

    /// Maakt het CompetitionJoinRequest schema aan in CloudKit
    private func createJoinRequestSchema() async throws {
        print("🔧 Creating CompetitionJoinRequest schema...")

        // Maak een dummy record om het schema te initialiseren
        let dummyRequest = CompetitionJoinRequest(
            competitionId: UUID(),
            requesterId: "dummy",
            requesterName: "Schema Setup"
        )

        let database = container.publicCloudDatabase
        let record = dummyRequest.toCKRecord()

        do {
            // Probeer het record op te slaan
            _ = try await database.save(record)
            print("✅ CompetitionJoinRequest schema created successfully")

            // Verwijder het dummy record weer
            _ = try await database.deleteRecord(withID: record.recordID)
            print("🗑️ Dummy record cleaned up")
        } catch {
            print("❌ Error creating schema: \(error)")
            throw error
        }
    }

    /// Maakt het CompetitionJoinResponse schema aan in CloudKit
    private func createJoinResponseSchema() async throws {
        print("🔧 Creating CompetitionJoinResponse schema...")

        // Maak een dummy record om het schema te initialiseren
        let dummyResponse = CompetitionJoinResponse(
            joinRequestId: UUID(),
            competitionId: UUID(),
            responderId: "dummy",
            responderName: "Schema Setup",
            decision: .approved
        )

        let database = container.publicCloudDatabase
        let record = dummyResponse.toCKRecord()

        do {
            // Probeer het record op te slaan
            _ = try await database.save(record)
            print("✅ CompetitionJoinResponse schema created successfully")

            // Verwijder het dummy record weer
            _ = try await database.deleteRecord(withID: record.recordID)
            print("🗑️ Dummy response record cleaned up")
        } catch {
            print("❌ Error creating response schema: \(error)")
            throw error
        }
    }

    /// Keurt een join request goed (alleen voor eigenaar)
    func approveJoinRequest(_ joinRequest: CompetitionJoinRequest) async throws {
        guard let competition = currentCompetition,
              await isCurrentUserOwner(of: competition) else {
            throw CloudKitError.notAuthorized
        }

        guard let currentUserId = try await getCurrentUserID() else {
            throw CloudKitError.userNotFound
        }

        // Haal de eigenaar naam op
        let ownerName = try await getCurrentUserName()

        // Maak een response record aan (dit kan de eigenaar doen)
        let response = CompetitionJoinResponse(
            joinRequestId: joinRequest.id,
            competitionId: competition.id,
            responderId: currentUserId,
            responderName: ownerName,
            decision: .approved
        )

        let database = container.publicCloudDatabase
        let record = response.toCKRecord()
        _ = try await database.save(record)

        print("✅ Join request approved for user: \(joinRequest.requesterName)")
    }

    /// Wijst een join request af (alleen voor eigenaar)
    func rejectJoinRequest(_ joinRequest: CompetitionJoinRequest) async throws {
        guard let competition = currentCompetition,
              await isCurrentUserOwner(of: competition) else {
            throw CloudKitError.notAuthorized
        }

        guard let currentUserId = try await getCurrentUserID() else {
            throw CloudKitError.userNotFound
        }

        // Haal de eigenaar naam op
        let ownerName = try await getCurrentUserName()

        // Maak een response record aan (dit kan de eigenaar doen)
        let response = CompetitionJoinResponse(
            joinRequestId: joinRequest.id,
            competitionId: competition.id,
            responderId: currentUserId,
            responderName: ownerName,
            decision: .rejected
        )

        let database = container.publicCloudDatabase
        let record = response.toCKRecord()
        _ = try await database.save(record)

        print("❌ Join request rejected for user: \(joinRequest.requesterName)")
    }

    /// Haalt responses op voor join requests
    func getJoinRequestResponses(for joinRequestIds: [UUID]) async throws -> [CompetitionJoinResponse] {
        guard !joinRequestIds.isEmpty else { return [] }

        let database = container.publicCloudDatabase
        let joinRequestIdStrings = joinRequestIds.map { $0.uuidString }
        let predicate = NSPredicate(format: "joinRequestId IN %@", joinRequestIdStrings)
        let query = CKQuery(recordType: CompetitionJoinResponse.recordType, predicate: predicate)

        do {
            let result = try await database.records(matching: query)
            let responses = result.matchResults.compactMap { (_, result) -> CompetitionJoinResponse? in
                switch result {
                case .success(let record):
                    return CompetitionJoinResponse(from: record)
                case .failure(let error):
                    print("❌ Error fetching response record: \(error)")
                    return nil
                }
            }

            print("📋 Found \(responses.count) responses for \(joinRequestIds.count) join requests")
            return responses
        } catch {
            // Als het record type niet bestaat, maak het schema aan
            if let ckError = error as? CKError, ckError.code == .unknownItem {
                print("🔧 CompetitionJoinResponse schema not found, creating it...")
                try await createJoinResponseSchema()
                print("✅ CompetitionJoinResponse schema created, returning empty responses for now")
                return []
            }

            print("❌ Error fetching join request responses: \(error)")
            throw error
        }
    }
}

// MARK: - CloudKit Errors
enum CloudKitError: LocalizedError {
    case userNotFound
    case noCompetitionSelected
    case recordConversionFailed
    case notAuthorized
    case joinRequestAlreadyExists
    case schemaNotConfigured
    case alreadyJoined

    var errorDescription: String? {
        switch self {
        case .userNotFound:
            return "Gebruiker niet gevonden"
        case .noCompetitionSelected:
            return "Geen competitie geselecteerd"
        case .recordConversionFailed:
            return "Fout bij converteren van record"
        case .notAuthorized:
            return "Geen toestemming voor deze actie"
        case .joinRequestAlreadyExists:
            return "Er is al een aanvraag ingediend voor deze competitie"
        case .alreadyJoined:
            return "Je bent al lid van deze competitie"
        case .schemaNotConfigured:
            return "CloudKit schema is niet geconfigureerd. Configureer de record types in CloudKit Console."
        }
    }
}
