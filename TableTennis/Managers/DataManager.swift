import Foundation
import SwiftUI

/// Centrale data manager voor de app
class DataManager: ObservableObject {
    @Published var players: [Player] = []
    @Published var matches: [Match] = []
    @Published var isLoading = false

    // Debounce mechanism voor statistieken herberekening
    private var isRecalculatingStatistics = false
    private let recalculationQueue = DispatchQueue(label: "statistics-recalculation", qos: .userInitiated)
    @Published var errorMessage: String?

    let cloudKitManager = CloudKitManager.shared

    init() {
        // Data wordt geladen wanneer een competitie wordt geselecteerd
    }

    // MARK: - Data Loading

    /// Laadt alle data voor de huidige competitie
    func loadData() async {
        print("🔄 DataManager: Starting to load data...")

        await MainActor.run {
            isLoading = true
            errorMessage = nil
        }

        // Fetch players and matches separately to handle errors independently
        var fetchedPlayers: [Player] = []
        var fetchedMatches: [Match] = []
        var errors: [String] = []

        // Fetch players
        do {
            print("📡 DataManager: Fetching players...")
            fetchedPlayers = try await cloudKitManager.fetchPlayers()
            print("✅ DataManager: Successfully fetched \(fetchedPlayers.count) players")
        } catch {
            print("❌ DataManager: Error fetching players: \(error)")
            errors.append("Players: \(error.localizedDescription)")
        }

        // Fetch matches
        do {
            print("📡 DataManager: Fetching matches...")
            fetchedMatches = try await cloudKitManager.fetchMatches()
            print("✅ DataManager: Successfully fetched \(fetchedMatches.count) matches")

            // Debug: Check games in each match
            for (index, match) in fetchedMatches.enumerated() {
                print("🎮 DEBUG: Match \(index + 1) (\(match.id)): \(match.games.count) games")
                for (gameIndex, game) in match.games.enumerated() {
                    print("🎮 DEBUG: - Game \(gameIndex + 1): \(game.team1Score)-\(game.team2Score), completed: \(game.isCompleted)")
                }
            }
        } catch {
            print("❌ DataManager: Error fetching matches: \(error)")
            errors.append("Matches: \(error.localizedDescription)")
        }

        // Update UI with whatever data we successfully fetched
        await MainActor.run {
            self.players = fetchedPlayers
            self.matches = fetchedMatches
            self.isLoading = false

            if errors.isEmpty {
                print("🔄 DataManager: UI updated with all data successfully")
                self.errorMessage = nil
            } else {
                print("⚠️ DataManager: UI updated with partial data. Errors: \(errors)")
                self.errorMessage = "Gedeeltelijk geladen: \(errors.joined(separator: ", "))"
            }

            print("🔄 DataManager: Final state - \(self.players.count) players, \(self.matches.count) matches")
        }
    }

    /// Herlaadt alle data
    func refreshData() {
        Task {
            await loadData()
        }
    }
    
    // MARK: - Player Management

    /// Voegt een nieuwe speler toe
    func addPlayer(_ player: Player) {
        Task {
            do {
                print("💾 DataManager: Attempting to add player \(player.name)")
                try await cloudKitManager.savePlayer(player)
                print("✅ DataManager: Successfully added player \(player.name)")

                await MainActor.run {
                    self.players.append(player)
                    print("🔄 DataManager: Added player to local array")
                }
            } catch {
                print("❌ DataManager: Error adding player \(player.name): \(error)")
                await MainActor.run {
                    self.errorMessage = "Fout bij toevoegen speler: \(error.localizedDescription)"
                }
            }
        }
    }

    /// Update een bestaande speler
    func updatePlayer(_ player: Player) {
        Task {
            do {
                print("💾 DataManager: Attempting to update player \(player.name)")
                try await cloudKitManager.savePlayer(player)
                print("✅ DataManager: Successfully updated player \(player.name)")

                await MainActor.run {
                    if let index = self.players.firstIndex(where: { $0.id == player.id }) {
                        self.players[index] = player
                        print("🔄 DataManager: Updated player in local array at index \(index)")
                    } else {
                        print("⚠️ DataManager: Could not find player \(player.name) in local array")
                    }
                }
            } catch {
                print("❌ DataManager: Error updating player \(player.name): \(error)")
                await MainActor.run {
                    self.errorMessage = "Fout bij updaten speler: \(error.localizedDescription)"
                }
            }
        }
    }

    /// Verwijdert een speler
    func deletePlayer(_ player: Player) {
        // Voor nu alleen lokaal verwijderen
        // In productie zou je ook CloudKit record verwijderen
        players.removeAll { $0.id == player.id }
    }

    /// Zoekt een speler op ID
    func player(withId id: UUID) -> Player? {
        return players.first { $0.id == id }
    }
    
    // MARK: - Match Management

    /// Voegt een nieuwe wedstrijd toe
    func addMatch(_ match: Match) {
        print("💾 DataManager: Attempting to add match \(match.id)")
        print("📋 DataManager: Match details - Type: \(match.type), Status: \(match.status)")
        print("📋 DataManager: Match players - \(match.team1Player1.name) vs \(match.team2Player1.name)")
        print("📋 DataManager: Match competitionId: \(match.competitionId)")

        Task {
            do {
                print("💾 DataManager: Saving match to CloudKit...")
                try await cloudKitManager.saveMatch(match)
                print("✅ DataManager: Successfully saved match to CloudKit")

                await MainActor.run {
                    self.matches.append(match)
                    print("🔄 DataManager: Added match to local array. Total matches: \(self.matches.count)")
                }
            } catch {
                print("❌ DataManager: Error adding match: \(error)")
                await MainActor.run {
                    self.errorMessage = "Fout bij toevoegen wedstrijd: \(error.localizedDescription)"
                }
            }
        }
    }

    /// Update een bestaande wedstrijd
    func updateMatch(_ match: Match) {
        // Update lokale array eerst (voor live scoring)
        if let index = matches.firstIndex(where: { $0.id == match.id }) {
            let oldMatch = matches[index]
            matches[index] = match
            print("🔄 DataManager: Updated match in local array at index \(index)")

            // Alleen herberekenen als de match status verandert naar completed
            // (niet bij elke punt update tijdens live play)
            if match.status == .completed && oldMatch.status != .completed {
                print("🔄 Match completed - updating player statistics...")

                // Sla ELO ratings op vóór de update
                let eloRatingsBefore = Dictionary(uniqueKeysWithValues:
                    match.allPlayers.map { ($0.id, $0.eloRating) }
                )

                updatePlayerStatistics(for: match)

                // Bereken en sla ELO-veranderingen op in de match
                var eloChanges: [UUID: Double] = [:]
                for player in match.allPlayers {
                    let oldRating = eloRatingsBefore[player.id] ?? player.eloRating
                    let newRating = players.first(where: { $0.id == player.id })?.eloRating ?? player.eloRating
                    eloChanges[player.id] = newRating - oldRating
                    print("🎯 ELO change for \(player.name): \(oldRating) → \(newRating) (change: \(newRating - oldRating))")
                }

                // Update de match met ELO changes (alleen als Mix & Match nog geen ELO changes heeft)
                if let matchIndex = matches.firstIndex(where: { $0.id == match.id }) {
                    // Voor Mix & Match: gebruik de ELO changes die al zijn berekend in updateMixMatchEloRatings
                    if match.type == .mixMatch && !matches[matchIndex].eloChanges.isEmpty {
                        print("💾 Mix & Match ELO changes already calculated: \(matches[matchIndex].eloChanges)")
                    } else {
                        matches[matchIndex].eloChanges = eloChanges
                        print("💾 Saved ELO changes to match: \(eloChanges)")
                    }
                }

                // Synchroniseer alle bijgewerkte spelers naar CloudKit sequentieel
                Task {
                    do {
                        try await cloudKitManager.savePlayersSequentially(players)
                        print("💾 Player statistics synchronized to CloudKit!")
                    } catch {
                        print("❌ Error synchronizing players to CloudKit: \(error)")
                    }
                }
            }
        } else {
            print("⚠️ DataManager: Could not find match \(match.id) in local array")
        }

        // Probeer CloudKit op te slaan in de achtergrond
        Task {
            do {
                print("💾 DataManager: Attempting to save match \(match.id) to CloudKit")
                try await cloudKitManager.saveMatch(match)
                print("✅ DataManager: Successfully saved match \(match.id) to CloudKit")
            } catch {
                print("❌ DataManager: Error saving match \(match.id) to CloudKit: \(error)")
                await MainActor.run {
                    self.errorMessage = "CloudKit sync failed: \(error.localizedDescription)"
                }
            }
        }
    }

    /// Verwijdert een wedstrijd (synchrone versie voor UI consistency)
    func deleteMatchLocally(_ match: Match) {
        print("🗑️ Deleting match locally: \(match.team1Player1.name) vs \(match.team2Player1.name)")
        matches.removeAll { $0.id == match.id }
        print("🔄 Starting recalculation after match deletion...")
        recalculateAllPlayerStatistics()
    }

    /// Verwijdert een wedstrijd (asynchroon voor CloudKit)
    func deleteMatch(_ match: Match) {
        Task {
            do {
                try await cloudKitManager.deleteMatch(match)
                await MainActor.run {
                    print("🗑️ Deleting match: \(match.team1Player1.name) vs \(match.team2Player1.name)")
                    self.matches.removeAll { $0.id == match.id }
                    print("🔄 Starting recalculation after match deletion...")
                    self.recalculateAllPlayerStatistics()
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "Fout bij verwijderen wedstrijd: \(error.localizedDescription)"
                }
            }
        }
    }

    /// Zoekt een wedstrijd op ID
    func match(withId id: UUID) -> Match? {
        return matches.first { $0.id == id }
    }
    
    // MARK: - Statistics
    
    /// Update speler statistieken na een voltooide wedstrijd
    private func updatePlayerStatistics(for match: Match) {
        // Voor Mix & Match hoeven we geen winnaar te hebben om statistieken bij te werken
        let winner = match.winner
        if match.status != .completed || (winner == nil && match.type != .mixMatch) {
            print("❌ updatePlayerStatistics: Match not completed or no winner (and not Mix & Match)")
            return
        }

        print("📊 updatePlayerStatistics: Starting for match \(match.id)")
        print("📊 updatePlayerStatistics: Winner is \(winner)")

        let allPlayers = match.allPlayers
        print("📊 updatePlayerStatistics: All players: \(allPlayers.map { $0.name })")

        // Update ELO ratings eerst (één keer voor de hele wedstrijd)
        print("📊 updatePlayerStatistics: Updating ELO ratings...")
        updateEloRating(for: match.team1Player1, in: match)

        // Update andere statistieken per speler
        for player in allPlayers {
            if let index = players.firstIndex(where: { $0.id == player.id }) {
                // Update match statistics
                players[index].matchesPlayed += 1

                // Bepaal of deze speler heeft gewonnen
                let playerWon: Bool
                if match.type == .singles {
                    playerWon = (player.id == match.team1Player1.id && winner == .team1) ||
                               (player.id == match.team2Player1.id && winner == .team2)
                } else {
                    let isTeam1 = player.id == match.team1Player1.id || player.id == match.team1Player2?.id
                    let isTeam2 = player.id == match.team2Player1.id || player.id == match.team2Player2?.id
                    playerWon = (isTeam1 && winner == .team1) || (isTeam2 && winner == .team2)
                }

                if playerWon {
                    players[index].matchesWon += 1
                }

                // Update game statistics
                if match.type == .mixMatch {
                    // Voor Mix & Match: tel alleen games waarin deze speler daadwerkelijk speelde
                    for game in match.games {
                        guard game.isCompleted,
                              let team1Players = game.mixMatchTeam1Players,
                              let team2Players = game.mixMatchTeam2Players else { continue }

                        let playerInTeam1 = team1Players.contains(where: { $0.id == player.id })
                        let playerInTeam2 = team2Players.contains(where: { $0.id == player.id })

                        // Speler speelde in deze game
                        if playerInTeam1 || playerInTeam2 {
                            players[index].gamesPlayed += 1

                            // Check of speler deze game won
                            if let gameWinner = game.winner {
                                let gameWon = (playerInTeam1 && gameWinner == .team1) ||
                                             (playerInTeam2 && gameWinner == .team2)

                                if gameWon {
                                    players[index].gamesWon += 1
                                }
                            }
                        }
                    }
                } else {
                    // Voor Singles en Doubles: originele logica
                    for game in match.games {
                        players[index].gamesPlayed += 1

                        let gameWon: Bool
                        if match.type == .singles {
                            gameWon = (player.id == match.team1Player1.id && game.winner == .team1) ||
                                     (player.id == match.team2Player1.id && game.winner == .team2)
                        } else {
                            let isTeam1 = player.id == match.team1Player1.id || player.id == match.team1Player2?.id
                            let isTeam2 = player.id == match.team2Player1.id || player.id == match.team2Player2?.id
                            gameWon = (isTeam1 && game.winner == .team1) || (isTeam2 && game.winner == .team2)
                        }

                        if gameWon {
                            players[index].gamesWon += 1
                        }
                    }
                }
            }
        }

        print("📊 updatePlayerStatistics: Completed statistics update")
        print("📊 updatePlayerStatistics: Final player stats:")
        for player in allPlayers {
            if let index = players.firstIndex(where: { $0.id == player.id }) {
                let p = players[index]
                print("📊   \(p.name): ELO \(p.eloRating), Matches \(p.matchesPlayed)/\(p.matchesWon), Games \(p.gamesPlayed)/\(p.gamesWon)")
            }
        }

        // Players worden automatisch gesynchroniseerd via CloudKit
    }
    
    /// Update ELO rating voor een speler na een wedstrijd
    private func updateEloRating(for player: Player, in match: Match) {
        let winner = match.winner

        if match.type == .mixMatch {
            // Voor Mix & Match: update ELO per game (geen winnaar nodig)
            updateMixMatchEloRatings(for: match)
        } else if match.type == .singles {
            // Voor singles: winnaar is vereist
            guard let winner = winner else {
                print("❌ No winner found for singles match - skipping ELO update")
                return
            }

            // Gebruik EloCalculator voor singles
            let newRatings = EloCalculator.calculateSinglesMatch(
                player1: match.team1Player1,
                player2: match.team2Player1,
                winner: winner
            )

            // Update beide spelers
            if let index1 = players.firstIndex(where: { $0.id == match.team1Player1.id }) {
                players[index1].eloRating = newRatings.player1NewRating
            }
            if let index2 = players.firstIndex(where: { $0.id == match.team2Player1.id }) {
                players[index2].eloRating = newRatings.player2NewRating
            }
        } else {
            // Voor doubles: winnaar is vereist
            guard let winner = winner else {
                print("❌ No winner found for doubles match - skipping ELO update")
                return
            }

            // Gebruik EloCalculator voor doubles
            guard let team1Player2 = match.team1Player2,
                  let team2Player2 = match.team2Player2 else { return }

            let newRatings = EloCalculator.calculateDoublesMatch(
                team1Player1: match.team1Player1,
                team1Player2: team1Player2,
                team2Player1: match.team2Player1,
                team2Player2: team2Player2,
                winner: winner
            )

            // Update alle vier spelers
            if let index1 = players.firstIndex(where: { $0.id == match.team1Player1.id }) {
                players[index1].eloRating = newRatings.team1Player1NewRating
            }
            if let index2 = players.firstIndex(where: { $0.id == team1Player2.id }) {
                players[index2].eloRating = newRatings.team1Player2NewRating
            }
            if let index3 = players.firstIndex(where: { $0.id == match.team2Player1.id }) {
                players[index3].eloRating = newRatings.team2Player1NewRating
            }
            if let index4 = players.firstIndex(where: { $0.id == team2Player2.id }) {
                players[index4].eloRating = newRatings.team2Player2NewRating
            }
        }
    }

    /// Update ELO ratings voor Mix & Match wedstrijd (per game)
    private func updateMixMatchEloRatings(for match: Match) {
        guard match.type == .mixMatch else { return }

        print("🎯 Starting Mix & Match ELO calculation for match \(match.id)")
        print("🎯 Match has \(match.games.count) games, \(match.games.filter { $0.isCompleted }.count) completed")

        // Track ELO changes per player voor opslag in match
        var totalEloChanges: [UUID: Double] = [:]

        // Initialize ELO changes for all Mix & Match players
        for player in match.mixMatchPlayers {
            totalEloChanges[player.id] = 0.0
            print("🎯 Initialized ELO tracking for \(player.name)")
        }

        // Update ELO per voltooide game - gebruik dezelfde logica als MatchDetailView
        for (gameIndex, game) in match.games.enumerated() {
            guard game.isCompleted,
                  let gameWinner = game.winner,
                  let team1Players = game.mixMatchTeam1Players,
                  let team2Players = game.mixMatchTeam2Players else { continue }

            guard team1Players.count == 2 && team2Players.count == 2 else { continue }

            // Track ELO changes for this specific game
            var gameEloChanges: [UUID: Double] = [:]

            // Update ELO voor elke speler die in deze game speelde
            for player in match.mixMatchPlayers {
                guard let playerIndex = players.firstIndex(where: { $0.id == player.id }) else { continue }

                // Check of deze speler in deze game speelde
                let playerInTeam1 = team1Players.contains(where: { $0.id == player.id })
                let playerInTeam2 = team2Players.contains(where: { $0.id == player.id })

                guard playerInTeam1 || playerInTeam2 else { continue }

                // Bereken ELO verandering voor deze game - consistent met MatchDetailView
                let teamPlayers = playerInTeam1 ? team1Players : team2Players
                let opponentPlayers = playerInTeam1 ? team2Players : team1Players

                let opponentAverageRating = (opponentPlayers[0].eloRating + opponentPlayers[1].eloRating) / 2.0

                // Voor Mix & Match: gebruik win/loss in plaats van score ratio
                let playerWon = (playerInTeam1 && gameWinner == .team1) ||
                               (!playerInTeam1 && gameWinner == .team2)
                let actualScore = playerWon ? 1.0 : 0.0

                let gameChange = EloCalculator.ratingChange(
                    currentRating: players[playerIndex].eloRating,
                    opponentRating: opponentAverageRating,
                    actualScore: actualScore,
                    kFactor: EloCalculator.defaultKFactor * 0.75 // Consistent met MatchDetailView
                )



                // Update de speler rating
                players[playerIndex].eloRating += gameChange

                // Store ELO change for this game
                gameEloChanges[player.id] = gameChange

                // Accumulate ELO change for this player
                totalEloChanges[player.id, default: 0.0] += gameChange
            }

            // Store ELO changes in the game
            if let matchIndex = matches.firstIndex(where: { $0.id == match.id }) {
                matches[matchIndex].games[gameIndex].eloChanges = gameEloChanges
                print("🎯 Game \(gameIndex + 1) ELO changes: \(gameEloChanges.mapValues { Int($0.rounded()) })")
            }
        }

        // Store the total ELO changes in the match
        if let matchIndex = matches.firstIndex(where: { $0.id == match.id }) {
            matches[matchIndex].eloChanges = totalEloChanges
            print("🎯 Mix & Match ELO changes stored: \(totalEloChanges.mapValues { Int($0.rounded()) })")

            // Save the updated match to CloudKit
            updateMatch(matches[matchIndex])
        }
    }

    /// Herberekent alle speler statistieken vanaf het begin
    func recalculateAllPlayerStatistics() {
        // Voorkom dubbele herberekeningen
        guard !isRecalculatingStatistics else {
            print("⏳ DataManager: Statistics recalculation already in progress, skipping...")
            return
        }

        isRecalculatingStatistics = true
        defer { isRecalculatingStatistics = false }

        print("🔄 Starting recalculation of all player statistics...")
        print("📊 Players before reset: \(players.map { "\($0.name): ELO \($0.eloRating), Matches \($0.matchesPlayed)/\($0.matchesWon)" })")
        print("🆔 Current player IDs: \(players.map { "\($0.name): \($0.id)" })")

        // Reset alle speler statistieken
        for index in players.indices {
            players[index].eloRating = 1200.0
            players[index].matchesPlayed = 0
            players[index].matchesWon = 0
            players[index].gamesPlayed = 0
            players[index].gamesWon = 0
        }

        print("🔄 Players after reset: \(players.map { "\($0.name): ELO \($0.eloRating), Matches \($0.matchesPlayed)/\($0.matchesWon)" })")

        // Sorteer alle voltooide wedstrijden op datum
        let completedMatches = matches
            .filter { $0.status == .completed }
            .sorted {
                ($0.completedAt ?? Date.distantPast) < ($1.completedAt ?? Date.distantPast)
            }

        print("📅 Found \(completedMatches.count) completed matches to recalculate")

        // Herbereken alle statistieken voor elke wedstrijd in chronologische volgorde
        for (index, match) in completedMatches.enumerated() {
            print("🏓 Recalculating match \(index + 1)/\(completedMatches.count): \(match.team1Player1.name) vs \(match.team2Player1.name)")
            print("🏓   Match details: Type=\(match.type.rawValue), Status=\(match.status.rawValue), Winner=\(match.winner?.rawValue ?? "none")")
            print("🏓   Games: \(match.games.count), Team1 won: \(match.team1GamesWon), Team2 won: \(match.team2GamesWon)")

            recalculateStatisticsForMatch(match)

            print("🏓   After processing this match:")
            for player in match.allPlayers {
                if let index = players.firstIndex(where: { $0.id == player.id }) {
                    let p = players[index]
                    print("🏓     \(p.name): ELO \(p.eloRating), Matches \(p.matchesPlayed)/\(p.matchesWon), Games \(p.gamesPlayed)/\(p.gamesWon)")
                }
            }
        }

        print("✅ Players after recalculation: \(players.map { "\($0.name): ELO \($0.eloRating), Matches \($0.matchesPlayed)/\($0.matchesWon)" })")

        // Synchroniseer alle bijgewerkte spelers naar CloudKit sequentieel
        Task {
            do {
                try await cloudKitManager.savePlayersSequentially(players)
                print("💾 Player statistics synchronized to CloudKit!")
            } catch {
                print("❌ Error synchronizing players to CloudKit: \(error)")
            }
        }
    }

    /// Herberekent alle statistieken voor een specifieke wedstrijd (gebruikt voor herberekening)
    private func recalculateStatisticsForMatch(_ match: Match) {
        print("🔄 RECALCULATE: Starting for match \(match.team1Player1.name) vs \(match.team2Player1.name)")
        print("🔄 RECALCULATE: Match type=\(match.type.rawValue), status=\(match.status.rawValue)")
        print("🔄 RECALCULATE: Games count=\(match.games.count), isCompleted=\(match.isCompleted)")

        // Voor Mix & Match hoeven we geen winnaar te hebben om statistieken bij te werken
        let winner = match.winner
        if winner == nil && match.type != .mixMatch {
            print("❌ RECALCULATE: No winner found for non-Mix & Match - skipping")
            print("❌ RECALCULATE: Team1 games won: \(match.team1GamesWon), Team2 games won: \(match.team2GamesWon)")
            return
        }

        let allPlayers = match.allPlayers
        print("👥 RECALCULATE: Match players: \(allPlayers.map { $0.name })")
        print("🏆 RECALCULATE: Winner: \(String(describing: winner))")

        // Update statistieken per speler
        for player in allPlayers {
            print("🔍 Processing player: \(player.name) (ID: \(player.id))")

            if let index = players.firstIndex(where: { $0.id == player.id }) {
                print("✅ Found player at index \(index)")

                // Update match statistics
                players[index].matchesPlayed += 1
                print("📊 Updated matches played to: \(players[index].matchesPlayed)")

                // Update game statistics
                print("🎮 Processing \(match.games.count) games")

                if match.type == .mixMatch {
                    // Voor Mix & Match: tel alleen games waarin deze speler daadwerkelijk speelde
                    for (gameIndex, game) in match.games.enumerated() {
                        guard game.isCompleted,
                              let team1Players = game.mixMatchTeam1Players,
                              let team2Players = game.mixMatchTeam2Players else {
                            print("⚠️ Skipping incomplete game \(gameIndex + 1)")
                            continue
                        }

                        let playerInTeam1 = team1Players.contains(where: { $0.id == player.id })
                        let playerInTeam2 = team2Players.contains(where: { $0.id == player.id })

                        // Speler speelde in deze game
                        if playerInTeam1 || playerInTeam2 {
                            print("🎮 Processing Mix & Match game \(gameIndex + 1) for \(player.name)")
                            players[index].gamesPlayed += 1

                            // Check of speler deze game won
                            if let gameWinner = game.winner {
                                let gameWon = (playerInTeam1 && gameWinner == .team1) ||
                                             (playerInTeam2 && gameWinner == .team2)

                                if gameWon {
                                    players[index].gamesWon += 1
                                }

                                print("🏆 Game \(gameIndex + 1): won=\(gameWon), total games won: \(players[index].gamesWon)")
                            }
                        } else {
                            print("⏭️ Player \(player.name) did not play in game \(gameIndex + 1)")
                        }
                    }
                } else {
                    // Voor Singles en Doubles: originele logica
                    for (gameIndex, game) in match.games.enumerated() {
                        players[index].gamesPlayed += 1

                        let gameWon: Bool
                        if match.type == .singles {
                            gameWon = (player.id == match.team1Player1.id && game.winner == .team1) ||
                                     (player.id == match.team2Player1.id && game.winner == .team2)
                        } else {
                            let isTeam1 = player.id == match.team1Player1.id || player.id == match.team1Player2?.id
                            let isTeam2 = player.id == match.team2Player1.id || player.id == match.team2Player2?.id
                            gameWon = (isTeam1 && game.winner == .team1) || (isTeam2 && game.winner == .team2)
                        }

                        if gameWon {
                            players[index].gamesWon += 1
                        }

                        print("🎮 Game \(gameIndex + 1): won=\(gameWon), total games won: \(players[index].gamesWon)")
                    }
                }

                // Bepaal of deze speler de match won
                let playerWon: Bool
                if match.type == .mixMatch {
                    // Voor Mix & Match: speler wint als ze de meeste games hebben gewonnen
                    let gamesWonByPlayer = match.games.filter { game in
                        guard let gameWinner = game.winner else { return false }
                        if gameWinner == .team1 {
                            return game.mixMatchTeam1Players?.contains(where: { $0.id == player.id }) ?? false
                        } else {
                            return game.mixMatchTeam2Players?.contains(where: { $0.id == player.id }) ?? false
                        }
                    }.count

                    let maxGamesWon = match.mixMatchPlayers.map { p in
                        match.games.filter { game in
                            guard let gameWinner = game.winner else { return false }
                            if gameWinner == .team1 {
                                return game.mixMatchTeam1Players?.contains(where: { $0.id == p.id }) ?? false
                            } else {
                                return game.mixMatchTeam2Players?.contains(where: { $0.id == p.id }) ?? false
                            }
                        }.count
                    }.max() ?? 0

                    playerWon = gamesWonByPlayer == maxGamesWon && maxGamesWon > 0
                } else if match.type == .singles {
                    playerWon = winner != nil && ((player.id == match.team1Player1.id && winner == .team1) ||
                               (player.id == match.team2Player1.id && winner == .team2))
                } else {
                    // Voor Doubles: alleen als er een winnaar is
                    if let winner = winner {
                        let isTeam1 = player.id == match.team1Player1.id || player.id == match.team1Player2?.id
                        let isTeam2 = player.id == match.team2Player1.id || player.id == match.team2Player2?.id
                        playerWon = (isTeam1 && winner == .team1) || (isTeam2 && winner == .team2)
                    } else {
                        playerWon = false
                    }
                }

                print("🏆 Player \(player.name) won: \(playerWon)")

                if playerWon {
                    players[index].matchesWon += 1
                    print("🎉 Updated matches won to: \(players[index].matchesWon)")
                }

                print("📈 Final stats for \(player.name): \(players[index].matchesPlayed) matches, \(players[index].matchesWon) won, \(players[index].gamesPlayed) games, \(players[index].gamesWon) games won")
            } else {
                print("❌ Player \(player.name) not found in players array!")
            }
        }

        // Sla ELO ratings op voordat we de match verwerken
        var eloRatingsBefore: [UUID: Double] = [:]
        for player in match.allPlayers {
            if let currentPlayer = players.first(where: { $0.id == player.id }) {
                eloRatingsBefore[player.id] = currentPlayer.eloRating
            }
        }

        // Update ELO ratings
        print("⚡ Updating ELO ratings...")
        updateEloRatingForMatch(match)

        // Bereken en sla ELO-veranderingen op
        var eloChanges: [UUID: Double] = [:]
        for player in match.allPlayers {
            let oldRating = eloRatingsBefore[player.id] ?? 1200.0
            let newRating = players.first(where: { $0.id == player.id })?.eloRating ?? 1200.0
            eloChanges[player.id] = newRating - oldRating
            print("📊 \(player.name): \(oldRating) → \(newRating) (change: \(newRating - oldRating))")
        }

        // Update de match met ELO changes
        if let matchIndex = matches.firstIndex(where: { $0.id == match.id }) {
            matches[matchIndex].eloChanges = eloChanges
            print("💾 Saved ELO changes to match: \(eloChanges)")
        }
    }

    /// Update ELO ratings voor een specifieke wedstrijd (gebruikt voor herberekening)
    private func updateEloRatingForMatch(_ match: Match) {
        // Voor Mix & Match kunnen we ELO berekenen ook zonder duidelijke winnaar
        let winner = match.winner
        if winner == nil && match.type != .mixMatch {
            print("❌ No winner for ELO calculation")
            return
        }

        if match.type == .mixMatch {
            print("🎯 Calculating ELO for Mix & Match")
            updateMixMatchEloRatings(for: match)
        } else if match.type == .singles {
            print("🎯 Calculating ELO for singles match")

            // Haal huidige speler data op
            guard let player1 = players.first(where: { $0.id == match.team1Player1.id }),
                  let player2 = players.first(where: { $0.id == match.team2Player1.id }) else {
                print("❌ Could not find players for ELO calculation")
                return
            }

            print("📊 Before ELO: \(player1.name)=\(player1.eloRating), \(player2.name)=\(player2.eloRating)")

            let newRatings = EloCalculator.calculateSinglesMatch(
                player1: player1,
                player2: player2,
                winner: winner!
            )

            print("📊 New ELO calculated: \(player1.name)=\(newRatings.player1NewRating), \(player2.name)=\(newRatings.player2NewRating)")

            // Update beide spelers
            if let index1 = players.firstIndex(where: { $0.id == match.team1Player1.id }) {
                players[index1].eloRating = newRatings.player1NewRating
                print("✅ Updated \(players[index1].name) ELO to \(players[index1].eloRating)")
            }
            if let index2 = players.firstIndex(where: { $0.id == match.team2Player1.id }) {
                players[index2].eloRating = newRatings.player2NewRating
                print("✅ Updated \(players[index2].name) ELO to \(players[index2].eloRating)")
            }
        } else {
            print("🎯 Calculating ELO for doubles match")

            // Haal huidige speler data op
            guard let team1Player1 = players.first(where: { $0.id == match.team1Player1.id }),
                  let team1Player2 = players.first(where: { $0.id == match.team1Player2?.id }),
                  let team2Player1 = players.first(where: { $0.id == match.team2Player1.id }),
                  let team2Player2 = players.first(where: { $0.id == match.team2Player2?.id }) else {
                print("❌ Could not find all players for doubles ELO calculation")
                return
            }

            let newRatings = EloCalculator.calculateDoublesMatch(
                team1Player1: team1Player1,
                team1Player2: team1Player2,
                team2Player1: team2Player1,
                team2Player2: team2Player2,
                winner: winner!
            )

            // Update alle vier spelers
            if let index1 = players.firstIndex(where: { $0.id == match.team1Player1.id }) {
                players[index1].eloRating = newRatings.team1Player1NewRating
                print("✅ Updated \(players[index1].name) ELO to \(players[index1].eloRating)")
            }
            if let index2 = players.firstIndex(where: { $0.id == match.team1Player2?.id }) {
                players[index2].eloRating = newRatings.team1Player2NewRating
                print("✅ Updated \(players[index2].name) ELO to \(players[index2].eloRating)")
            }
            if let index3 = players.firstIndex(where: { $0.id == match.team2Player1.id }) {
                players[index3].eloRating = newRatings.team2Player1NewRating
                print("✅ Updated \(players[index3].name) ELO to \(players[index3].eloRating)")
            }
            if let index4 = players.firstIndex(where: { $0.id == match.team2Player2?.id }) {
                players[index4].eloRating = newRatings.team2Player2NewRating
                print("✅ Updated \(players[index4].name) ELO to \(players[index4].eloRating)")
            }
        }
    }
    
    /// Geeft wedstrijden voor een specifieke speler
    func matches(for player: Player) -> [Match] {
        return matches.filter { match in
            match.allPlayers.contains { $0.id == player.id }
        }
    }
    
    /// Geeft voltooide wedstrijden gesorteerd op datum
    var completedMatches: [Match] {
        return matches
            .filter { $0.status == .completed }
            .sorted { $0.completedAt ?? Date.distantPast > $1.completedAt ?? Date.distantPast }
    }
    
    /// Geeft spelers gesorteerd op ELO rating
    var playersByElo: [Player] {
        return players.sorted { $0.eloRating > $1.eloRating }
    }

    /// Publieke functie om handmatig alle statistieken te herberekenen (voor debugging)
    func forceRecalculateAllStatistics() {
        // Voorkom dubbele herberekeningen
        guard !isRecalculatingStatistics else {
            print("⏳ DataManager: Manual recalculation already in progress, skipping...")
            return
        }

        print("🔧 Manual recalculation triggered...")
        print("🔧 Current state before recalculation:")
        print("🔧   Total players: \(players.count)")
        print("🔧   Total matches: \(matches.count)")
        print("🔧   Completed matches: \(matches.filter { $0.status == .completed }.count)")

        for player in players {
            print("🔧   Player \(player.name): ELO \(player.eloRating), Matches \(player.matchesPlayed)/\(player.matchesWon), Games \(player.gamesPlayed)/\(player.gamesWon)")
        }

        for match in matches.filter({ $0.status == .completed }) {
            print("🔧   Match: \(match.team1Player1.name) vs \(match.team2Player1.name), Winner: \(match.winner?.rawValue ?? "none"), Games: \(match.games.count)")
            if match.type == .mixMatch {
                print("🔧     Mix & Match players: \(match.allPlayers.map { $0.name })")
                for (index, game) in match.games.enumerated() {
                    if let team1 = game.mixMatchTeam1Players, let team2 = game.mixMatchTeam2Players {
                        print("🔧     Game \(index + 1): \(team1.map { $0.name }) vs \(team2.map { $0.name }), Winner: \(game.winner?.rawValue ?? "none")")
                    }
                }
            }
        }

        recalculateAllPlayerStatistics()

        print("🔧 Final state after recalculation:")
        for player in players {
            print("🔧   Player \(player.name): ELO \(player.eloRating), Matches \(player.matchesPlayed)/\(player.matchesWon), Games \(player.gamesPlayed)/\(player.gamesWon)")
        }
    }

    /// Migreert bestaande matches naar de nieuwe ELO-veranderingen opzet
    func migrateExistingMatchesToEloChanges() {
        print("🔄 Starting migration of existing matches to ELO changes format...")

        // Filter voltooide matches die nog geen ELO changes hebben
        let matchesToMigrate = matches.filter { match in
            match.status == .completed && match.eloChanges.isEmpty
        }

        guard !matchesToMigrate.isEmpty else {
            print("✅ No matches need migration - all matches already have ELO changes stored")
            return
        }

        print("📊 Found \(matchesToMigrate.count) matches that need migration")

        // Sorteer matches op voltooiingsdatum om chronologische volgorde te behouden
        let sortedMatches = matchesToMigrate.sorted {
            ($0.completedAt ?? Date.distantPast) < ($1.completedAt ?? Date.distantPast)
        }

        // Maak een kopie van spelers met reset ELO ratings voor simulatie
        var simulatedPlayers = players.map { player in
            var copy = player
            copy.eloRating = 1200.0 // Reset naar start ELO
            return copy
        }

        print("🔄 Simulating ELO progression through \(sortedMatches.count) matches...")

        // Simuleer elke match en bereken ELO-veranderingen
        for (index, match) in sortedMatches.enumerated() {
            print("🎯 Processing match \(index + 1)/\(sortedMatches.count): \(match.id)")

            // Sla ELO ratings op vóór deze match
            let eloRatingsBefore = Dictionary(uniqueKeysWithValues:
                match.allPlayers.compactMap { matchPlayer in
                    if let simulatedPlayer = simulatedPlayers.first(where: { $0.id == matchPlayer.id }) {
                        return (matchPlayer.id, simulatedPlayer.eloRating)
                    }
                    return nil
                }
            )

            // Simuleer ELO update voor deze match
            simulateEloUpdateForMatch(match, simulatedPlayers: &simulatedPlayers)

            // Bereken ELO-veranderingen
            var eloChanges: [UUID: Double] = [:]
            for player in match.allPlayers {
                let oldRating = eloRatingsBefore[player.id] ?? 1200.0
                let newRating = simulatedPlayers.first(where: { $0.id == player.id })?.eloRating ?? 1200.0
                eloChanges[player.id] = newRating - oldRating
            }

            // Update de match met berekende ELO changes
            if let matchIndex = matches.firstIndex(where: { $0.id == match.id }) {
                matches[matchIndex].eloChanges = eloChanges
                print("💾 Stored ELO changes for match: \(eloChanges)")
            }
        }

        print("✅ Migration completed! Updated \(sortedMatches.count) matches with ELO changes")

        // Synchroniseer alle bijgewerkte matches naar CloudKit
        Task {
            do {
                print("☁️ Synchronizing migrated matches to CloudKit...")
                for match in sortedMatches {
                    if let updatedMatch = matches.first(where: { $0.id == match.id }) {
                        try await cloudKitManager.saveMatch(updatedMatch)
                    }
                }
                print("✅ All migrated matches synchronized to CloudKit!")
            } catch {
                print("❌ Error synchronizing migrated matches to CloudKit: \(error)")
            }
        }
    }

    /// Herbereken alle ELO-ratings vanaf het begin op basis van wedstrijdgeschiedenis
    func recalculateAllEloRatings() {
        print("🔄 Starting complete ELO recalculation...")

        // Reset alle spelers naar startrating
        for index in players.indices {
            players[index].eloRating = 1200.0
        }

        // Sorteer alle voltooide matches op datum
        let sortedMatches = matches
            .filter { $0.status == .completed && $0.completedAt != nil }
            .sorted { $0.completedAt! < $1.completedAt! }

        print("📊 Recalculating ELO for \(sortedMatches.count) matches in chronological order...")

        // Verwerk elke match in chronologische volgorde
        for match in sortedMatches {
            print("🎯 Processing match: \(match.team1Player1.name) vs \(match.team2Player1.name)")

            // Sla ELO ratings op vóór de match
            let eloRatingsBefore = Dictionary(uniqueKeysWithValues:
                match.allPlayers.map { player in
                    let currentPlayer = players.first(where: { $0.id == player.id })
                    return (player.id, currentPlayer?.eloRating ?? 1200.0)
                }
            )

            // Update ELO ratings voor deze match
            updateEloRatingForMatch(match)

            // Bereken en sla ELO-veranderingen op
            var eloChanges: [UUID: Double] = [:]
            for player in match.allPlayers {
                let oldRating = eloRatingsBefore[player.id] ?? 1200.0
                let newRating = players.first(where: { $0.id == player.id })?.eloRating ?? 1200.0
                eloChanges[player.id] = newRating - oldRating
                print("📊 \(player.name): \(oldRating) → \(newRating) (change: \(newRating - oldRating))")
            }

            // Update de match met nieuwe ELO changes
            if let matchIndex = matches.firstIndex(where: { $0.id == match.id }) {
                matches[matchIndex].eloChanges = eloChanges
            }
        }

        print("✅ ELO recalculation completed")
        print("📊 Final ratings:")
        for player in players.sorted(by: { $0.eloRating > $1.eloRating }) {
            print("   \(player.name): \(player.eloRating)")
        }
    }

    /// Simuleert ELO update voor een match (gebruikt voor migratie)
    private func simulateEloUpdateForMatch(_ match: Match, simulatedPlayers: inout [Player]) {
        guard let winner = match.winner else { return }

        if match.type == .mixMatch {
            // Voor Mix & Match: simuleer ELO per game
            simulateMixMatchEloRatings(for: match, simulatedPlayers: &simulatedPlayers)
        } else if match.type == .singles {
            // Simuleer singles ELO update
            guard let player1Index = simulatedPlayers.firstIndex(where: { $0.id == match.team1Player1.id }),
                  let player2Index = simulatedPlayers.firstIndex(where: { $0.id == match.team2Player1.id }) else {
                return
            }

            let newRatings = EloCalculator.calculateSinglesMatch(
                player1: simulatedPlayers[player1Index],
                player2: simulatedPlayers[player2Index],
                winner: winner
            )

            simulatedPlayers[player1Index].eloRating = newRatings.player1NewRating
            simulatedPlayers[player2Index].eloRating = newRatings.player2NewRating

        } else {
            // Simuleer doubles ELO update
            guard let team1Player1Index = simulatedPlayers.firstIndex(where: { $0.id == match.team1Player1.id }),
                  let team1Player2Index = simulatedPlayers.firstIndex(where: { $0.id == match.team1Player2?.id }),
                  let team2Player1Index = simulatedPlayers.firstIndex(where: { $0.id == match.team2Player1.id }),
                  let team2Player2Index = simulatedPlayers.firstIndex(where: { $0.id == match.team2Player2?.id }) else {
                return
            }

            let newRatings = EloCalculator.calculateDoublesMatch(
                team1Player1: simulatedPlayers[team1Player1Index],
                team1Player2: simulatedPlayers[team1Player2Index],
                team2Player1: simulatedPlayers[team2Player1Index],
                team2Player2: simulatedPlayers[team2Player2Index],
                winner: winner
            )

            simulatedPlayers[team1Player1Index].eloRating = newRatings.team1Player1NewRating
            simulatedPlayers[team1Player2Index].eloRating = newRatings.team1Player2NewRating
            simulatedPlayers[team2Player1Index].eloRating = newRatings.team2Player1NewRating
            simulatedPlayers[team2Player2Index].eloRating = newRatings.team2Player2NewRating
        }
    }

    /// Simuleert Mix & Match ELO ratings (gebruikt voor migratie)
    private func simulateMixMatchEloRatings(for match: Match, simulatedPlayers: inout [Player]) {
        guard match.type == .mixMatch else { return }

        // Voor elke game in de Mix & Match
        for game in match.games {
            guard let gameWinner = game.winner,
                  let team1Players = game.mixMatchTeam1Players,
                  let team2Players = game.mixMatchTeam2Players,
                  team1Players.count == 2,
                  team2Players.count == 2 else {
                continue
            }

            // Vind de speler indices voor deze game
            guard let team1Player1Index = simulatedPlayers.firstIndex(where: { $0.id == team1Players[0].id }),
                  let team1Player2Index = simulatedPlayers.firstIndex(where: { $0.id == team1Players[1].id }),
                  let team2Player1Index = simulatedPlayers.firstIndex(where: { $0.id == team2Players[0].id }),
                  let team2Player2Index = simulatedPlayers.firstIndex(where: { $0.id == team2Players[1].id }) else {
                continue
            }

            // Bereken nieuwe ELO ratings voor deze game
            let newRatings = EloCalculator.calculateDoublesMatch(
                team1Player1: simulatedPlayers[team1Player1Index],
                team1Player2: simulatedPlayers[team1Player2Index],
                team2Player1: simulatedPlayers[team2Player1Index],
                team2Player2: simulatedPlayers[team2Player2Index],
                winner: gameWinner
            )

            // Update de gesimuleerde spelers
            simulatedPlayers[team1Player1Index].eloRating = newRatings.team1Player1NewRating
            simulatedPlayers[team1Player2Index].eloRating = newRatings.team1Player2NewRating
            simulatedPlayers[team2Player1Index].eloRating = newRatings.team2Player1NewRating
            simulatedPlayers[team2Player2Index].eloRating = newRatings.team2Player2NewRating
        }
    }

    /// Debug functie om statistieken van een specifieke speler te controleren
    func debugPlayerStatistics(for player: Player) {
        print("🔍 DEBUG PLAYER STATISTICS for \(player.name)")
        print("🔍   Current stats: Matches \(player.matchesPlayed)/\(player.matchesWon), Games \(player.gamesPlayed)/\(player.gamesWon)")

        let playerMatches = matches.filter { match in
            match.allPlayers.contains { $0.id == player.id } && match.status == .completed
        }

        print("🔍   Found \(playerMatches.count) completed matches for this player:")

        var expectedMatchesPlayed = 0
        var expectedGamesPlayed = 0
        var expectedMatchesWon = 0
        var expectedGamesWon = 0

        for match in playerMatches {
            expectedMatchesPlayed += 1

            print("🔍   Match: \(match.team1Player1.name) vs \(match.team2Player1.name) (\(match.type.rawValue))")
            print("🔍     Winner: \(match.winner?.rawValue ?? "none")")

            // Check if player won this match
            if let winner = match.winner {
                let playerWon: Bool
                if match.type == .singles {
                    playerWon = (player.id == match.team1Player1.id && winner == .team1) ||
                               (player.id == match.team2Player1.id && winner == .team2)
                } else {
                    let isTeam1 = player.id == match.team1Player1.id || player.id == match.team1Player2?.id
                    let isTeam2 = player.id == match.team2Player1.id || player.id == match.team2Player2?.id
                    playerWon = (isTeam1 && winner == .team1) || (isTeam2 && winner == .team2)
                }

                if playerWon {
                    expectedMatchesWon += 1
                }
                print("🔍     Player won match: \(playerWon)")
            }

            // Count games
            if match.type == .mixMatch {
                print("🔍     Mix & Match games:")
                for (index, game) in match.games.enumerated() {
                    guard game.isCompleted,
                          let team1Players = game.mixMatchTeam1Players,
                          let team2Players = game.mixMatchTeam2Players else {
                        print("🔍       Game \(index + 1): Incomplete or missing team data")
                        continue
                    }

                    let playerInTeam1 = team1Players.contains(where: { $0.id == player.id })
                    let playerInTeam2 = team2Players.contains(where: { $0.id == player.id })

                    if playerInTeam1 || playerInTeam2 {
                        expectedGamesPlayed += 1

                        if let gameWinner = game.winner {
                            let gameWon = (playerInTeam1 && gameWinner == .team1) ||
                                         (playerInTeam2 && gameWinner == .team2)
                            if gameWon {
                                expectedGamesWon += 1
                            }
                            print("🔍       Game \(index + 1): Player participated, won: \(gameWon)")
                        } else {
                            print("🔍       Game \(index + 1): Player participated, no winner")
                        }
                    } else {
                        print("🔍       Game \(index + 1): Player did not participate")
                    }
                }
            } else {
                // Regular match - player plays all games
                let completedGames = match.games.filter { $0.isCompleted }
                expectedGamesPlayed += completedGames.count

                for game in completedGames {
                    if let gameWinner = game.winner {
                        let gameWon: Bool
                        if match.type == .singles {
                            gameWon = (player.id == match.team1Player1.id && gameWinner == .team1) ||
                                     (player.id == match.team2Player1.id && gameWinner == .team2)
                        } else {
                            let isTeam1 = player.id == match.team1Player1.id || player.id == match.team1Player2?.id
                            gameWon = (isTeam1 && gameWinner == .team1) || (!isTeam1 && gameWinner == .team2)
                        }

                        if gameWon {
                            expectedGamesWon += 1
                        }
                    }
                }
                print("🔍     Regular match: \(completedGames.count) games played")
            }
        }

        print("🔍   EXPECTED vs ACTUAL:")
        print("🔍     Matches played: \(expectedMatchesPlayed) vs \(player.matchesPlayed)")
        print("🔍     Matches won: \(expectedMatchesWon) vs \(player.matchesWon)")
        print("🔍     Games played: \(expectedGamesPlayed) vs \(player.gamesPlayed)")
        print("🔍     Games won: \(expectedGamesWon) vs \(player.gamesWon)")

        if expectedMatchesPlayed != player.matchesPlayed ||
           expectedMatchesWon != player.matchesWon ||
           expectedGamesPlayed != player.gamesPlayed ||
           expectedGamesWon != player.gamesWon {
            print("❌ MISMATCH DETECTED! Statistics are incorrect.")
        } else {
            print("✅ Statistics are correct.")
        }
    }
}
