import SwiftUI

struct ImageCropView: View {
    let image: UIImage
    let onComplete: (UIImage?) -> Void
    
    @State private var scale: CGFloat = 1.0
    @State private var offset: CGSize = .zero
    @State private var lastOffset: CGSize = .zero
    @State private var lastScale: CGFloat = 1.0
    
    private let cropSize: CGFloat = 250
    
    var body: some View {
        NavigationView {
            GeometryReader { geometry in
                ZStack {
                    Color.black.ignoresSafeArea()
                    
                    // Background image (dimmed)
                    Image(uiImage: image)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .scaleEffect(scale)
                        .offset(offset)
                        .opacity(0.3)
                    
                    // Crop overlay
                    Rectangle()
                        .fill(Color.clear)
                        .frame(width: cropSize, height: cropSize)
                        .overlay(
                            Rectangle()
                                .stroke(Color.white, lineWidth: 2)
                        )
                        .overlay(
                            // Corner indicators
                            VStack {
                                HStack {
                                    cornerIndicator
                                    Spacer()
                                    cornerIndicator
                                }
                                Spacer()
                                HStack {
                                    cornerIndicator
                                    Spacer()
                                    cornerIndicator
                                }
                            }
                        )
                    
                    // Cropped image preview
                    Image(uiImage: image)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .scaleEffect(scale)
                        .offset(offset)
                        .mask(
                            Rectangle()
                                .frame(width: cropSize, height: cropSize)
                        )
                }
                .clipped()
                .gesture(
                    SimultaneousGesture(
                        // Pan gesture
                        DragGesture()
                            .onChanged { value in
                                offset = CGSize(
                                    width: lastOffset.width + value.translation.width,
                                    height: lastOffset.height + value.translation.height
                                )
                            }
                            .onEnded { _ in
                                lastOffset = offset
                            },
                        
                        // Zoom gesture
                        MagnificationGesture()
                            .onChanged { value in
                                let newScale = lastScale * value

                                // Calculate minimum scale to fill crop area
                                let imageSize = image.size
                                let minScaleWidth = cropSize / imageSize.width
                                let minScaleHeight = cropSize / imageSize.height
                                let minScale = max(minScaleWidth, minScaleHeight)

                                // Allow zoom from minimum scale to 5x
                                scale = max(minScale, min(newScale, 5.0))
                            }
                            .onEnded { _ in
                                lastScale = scale
                            }
                    )
                )
                .onAppear {
                    // Set initial scale to ensure the crop area can be filled
                    let imageSize = image.size
                    let screenSize = geometry.size

                    // Calculate scale needed to fill the crop area
                    let scaleToFillWidth = cropSize / imageSize.width
                    let scaleToFillHeight = cropSize / imageSize.height

                    // Use the larger scale to ensure crop area can be completely filled
                    let minScale = max(scaleToFillWidth, scaleToFillHeight)

                    // Also consider screen size to avoid making image too large initially
                    let maxScreenScale = min(screenSize.width / imageSize.width, screenSize.height / imageSize.height) * 1.5

                    scale = max(minScale, min(maxScreenScale, 1.0))
                    lastScale = scale
                }
            }
            .navigationTitle("Crop Photo")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        onComplete(nil)
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        let croppedImage = cropImage()
                        onComplete(croppedImage)
                    }
                    .fontWeight(.semibold)
                }
            }
        }
    }
    
    private var cornerIndicator: some View {
        Rectangle()
            .fill(Color.white)
            .frame(width: 20, height: 3)
            .overlay(
                Rectangle()
                    .fill(Color.white)
                    .frame(width: 3, height: 20)
            )
    }
    
    private func cropImage() -> UIImage? {
        let imageSize = image.size

        // Calculate the displayed image size (how it appears on screen)
        let imageAspectRatio = imageSize.width / imageSize.height
        let displayedImageSize: CGSize

        // Determine how the image is displayed (aspect fit)
        if imageAspectRatio > 1 {
            // Image is wider than tall
            displayedImageSize = CGSize(width: imageSize.width * scale, height: imageSize.height * scale)
        } else {
            // Image is taller than wide or square
            displayedImageSize = CGSize(width: imageSize.width * scale, height: imageSize.height * scale)
        }

        // Calculate the center of the displayed image
        let imageCenterX = displayedImageSize.width / 2
        let imageCenterY = displayedImageSize.height / 2

        // Calculate the crop area center (accounting for offset)
        let cropCenterX = imageCenterX - offset.width
        let cropCenterY = imageCenterY - offset.height

        // Calculate crop rect in scaled image coordinates
        let cropRectInScaledImage = CGRect(
            x: cropCenterX - cropSize / 2,
            y: cropCenterY - cropSize / 2,
            width: cropSize,
            height: cropSize
        )

        // Convert to original image coordinates
        let cropRectInOriginalImage = CGRect(
            x: cropRectInScaledImage.origin.x / scale,
            y: cropRectInScaledImage.origin.y / scale,
            width: cropRectInScaledImage.width / scale,
            height: cropRectInScaledImage.height / scale
        )

        // Clamp to image bounds
        let clampedRect = CGRect(
            x: max(0, min(cropRectInOriginalImage.origin.x, imageSize.width - cropRectInOriginalImage.width)),
            y: max(0, min(cropRectInOriginalImage.origin.y, imageSize.height - cropRectInOriginalImage.height)),
            width: min(cropRectInOriginalImage.width, imageSize.width),
            height: min(cropRectInOriginalImage.height, imageSize.height)
        )

        // Crop the image
        guard let cgImage = image.cgImage?.cropping(to: clampedRect) else {
            return nil
        }

        // Create final cropped image
        let croppedImage = UIImage(cgImage: cgImage, scale: image.scale, orientation: image.imageOrientation)

        // Resize to crop size for consistency
        let renderer = UIGraphicsImageRenderer(size: CGSize(width: cropSize, height: cropSize))
        return renderer.image { _ in
            croppedImage.draw(in: CGRect(origin: .zero, size: CGSize(width: cropSize, height: cropSize)))
        }
    }
}

#Preview {
    ImageCropView(image: UIImage(systemName: "photo")!) { _ in
        // Preview completion handler
    }
}
