import SwiftUI

struct ImageCropView: View {
    let image: UIImage
    let onComplete: (UIImage?) -> Void
    
    @State private var scale: CGFloat = 1.0
    @State private var offset: CGSize = .zero
    @State private var lastOffset: CGSize = .zero
    @State private var lastScale: CGFloat = 1.0
    
    private let cropSize: CGFloat = 250
    
    var body: some View {
        NavigationView {
            GeometryReader { geometry in
                ZStack {
                    Color.black.ignoresSafeArea()
                    
                    // Background image (dimmed)
                    Image(uiImage: image)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .scaleEffect(scale)
                        .offset(offset)
                        .opacity(0.3)
                    
                    // Crop overlay
                    Rectangle()
                        .fill(Color.clear)
                        .frame(width: cropSize, height: cropSize)
                        .overlay(
                            Rectangle()
                                .stroke(Color.white, lineWidth: 2)
                        )
                        .overlay(
                            // Corner indicators
                            VStack {
                                HStack {
                                    cornerIndicator
                                    Spacer()
                                    cornerIndicator
                                }
                                Spacer()
                                HStack {
                                    cornerIndicator
                                    Spacer()
                                    cornerIndicator
                                }
                            }
                        )
                    
                    // Cropped image preview
                    Image(uiImage: image)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .scaleEffect(scale)
                        .offset(offset)
                        .mask(
                            Rectangle()
                                .frame(width: cropSize, height: cropSize)
                        )
                }
                .clipped()
                .gesture(
                    SimultaneousGesture(
                        // Pan gesture
                        DragGesture()
                            .onChanged { value in
                                offset = CGSize(
                                    width: lastOffset.width + value.translation.width,
                                    height: lastOffset.height + value.translation.height
                                )
                            }
                            .onEnded { _ in
                                lastOffset = offset
                            },
                        
                        // Zoom gesture
                        MagnificationGesture()
                            .onChanged { value in
                                let newScale = lastScale * value
                                scale = max(0.5, min(newScale, 3.0))
                            }
                            .onEnded { _ in
                                lastScale = scale
                            }
                    )
                )
                .onAppear {
                    // Center the image initially
                    let imageSize = image.size
                    let screenSize = geometry.size
                    let imageAspectRatio = imageSize.width / imageSize.height
                    let screenAspectRatio = screenSize.width / screenSize.height
                    
                    if imageAspectRatio > screenAspectRatio {
                        // Image is wider than screen
                        scale = screenSize.height / imageSize.height
                    } else {
                        // Image is taller than screen
                        scale = screenSize.width / imageSize.width
                    }
                    
                    lastScale = scale
                }
            }
            .navigationTitle("Crop Photo")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        onComplete(nil)
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        let croppedImage = cropImage()
                        onComplete(croppedImage)
                    }
                    .fontWeight(.semibold)
                }
            }
        }
    }
    
    private var cornerIndicator: some View {
        Rectangle()
            .fill(Color.white)
            .frame(width: 20, height: 3)
            .overlay(
                Rectangle()
                    .fill(Color.white)
                    .frame(width: 3, height: 20)
            )
    }
    
    private func cropImage() -> UIImage? {
        let renderer = UIGraphicsImageRenderer(size: CGSize(width: cropSize, height: cropSize))
        
        return renderer.image { context in
            // Calculate the crop rect in the original image coordinates
            let imageSize = image.size
            let scaledImageSize = CGSize(
                width: imageSize.width * scale,
                height: imageSize.height * scale
            )
            
            // Calculate the position of the crop area relative to the scaled image
            let cropRect = CGRect(
                x: (scaledImageSize.width - cropSize) / 2 - offset.width,
                y: (scaledImageSize.height - cropSize) / 2 - offset.height,
                width: cropSize,
                height: cropSize
            )
            
            // Convert back to original image coordinates
            let originalCropRect = CGRect(
                x: cropRect.origin.x / scale,
                y: cropRect.origin.y / scale,
                width: cropRect.width / scale,
                height: cropRect.height / scale
            )
            
            // Ensure the crop rect is within bounds
            let clampedRect = CGRect(
                x: max(0, min(originalCropRect.origin.x, imageSize.width - originalCropRect.width)),
                y: max(0, min(originalCropRect.origin.y, imageSize.height - originalCropRect.height)),
                width: min(originalCropRect.width, imageSize.width),
                height: min(originalCropRect.height, imageSize.height)
            )
            
            // Crop the image
            if let cgImage = image.cgImage?.cropping(to: clampedRect) {
                let croppedUIImage = UIImage(cgImage: cgImage, scale: image.scale, orientation: image.imageOrientation)
                croppedUIImage.draw(in: CGRect(origin: .zero, size: CGSize(width: cropSize, height: cropSize)))
            }
        }
    }
}

#Preview {
    ImageCropView(image: UIImage(systemName: "photo")!) { _ in
        // Preview completion handler
    }
}
