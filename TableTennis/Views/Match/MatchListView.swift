import SwiftUI

struct MatchListView: View {
    @EnvironmentObject var dataManager: DataManager
    @State private var showingAddMatch = false
    @Binding var showingSettings: Bool
    @State private var selectedFilter: MatchFilter = .all
    
    enum MatchFilter: String, CaseIterable {
        case all = "All"
        case completed = "Completed"
        case inProgress = "In Progress"
        case scheduled = "Scheduled"
        
        var status: MatchStatus? {
            switch self {
            case .all: return nil
            case .completed: return .completed
            case .inProgress: return .inProgress
            case .scheduled: return .scheduled
            }
        }
    }
    
    var filteredMatches: [Match] {
        var matches = dataManager.matches
        print("🔍 MatchListView: Starting with \(matches.count) total matches")

        // Filter by status
        if let status = selectedFilter.status {
            matches = matches.filter { $0.status == status }
            print("🔍 MatchListView: After status filter (\(status)): \(matches.count) matches")
        }

        // Sort by date (newest first)
        let sortedMatches = matches.sorted { match1, match2 in
            let date1 = match1.completedAt ?? match1.createdAt
            let date2 = match2.completedAt ?? match2.createdAt
            return date1 > date2
        }

        print("🔍 MatchListView: Final filtered matches: \(sortedMatches.count)")
        return sortedMatches
    }
    
    var body: some View {
        VStack {
            // Filter Controls
            VStack(spacing: 12) {
                Picker("Filter", selection: $selectedFilter) {
                    ForEach(MatchFilter.allCases, id: \.self) { filter in
                        Text(filter.rawValue).tag(filter)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
            }
            .padding(.horizontal)
            
            // Matches List
            if filteredMatches.isEmpty {
                EmptyStateView(
                    title: "No matches",
                    subtitle: "Add your first match to get started",
                    systemImage: "gamecontroller"
                )
            } else {
                List {
                    ForEach(filteredMatches) { match in
                        NavigationLink(destination: MatchDetailView(match: match)) {
                            MatchRowView(match: match)
                        }
                    }
                    .onDelete(perform: deleteMatches)
                }
                .refreshable {
                    await dataManager.loadData()
                }
            }
        }
        .navigationTitle("Matches")
        .navigationBarTitleDisplayMode(.large)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: { showingAddMatch = true }) {
                    Image(systemName: "plus")
                }
            }

            ToolbarItem(placement: .navigationBarLeading) {
                Button {
                    showingSettings = true
                } label: {
                    Image(systemName: "gear")
                }
            }
        }
        .sheet(isPresented: $showingAddMatch) {
            AddMatchView()
        }
    }
    
    private func deleteMatches(offsets: IndexSet) {
        // Collect matches to delete first
        let matchesToDelete = offsets.map { filteredMatches[$0] }

        // Remove from local array immediately to prevent UI inconsistency
        for match in matchesToDelete {
            if let index = dataManager.matches.firstIndex(where: { $0.id == match.id }) {
                dataManager.matches.remove(at: index)
            }
        }

        // Then delete from CloudKit asynchronously
        for match in matchesToDelete {
            Task {
                do {
                    try await dataManager.cloudKitManager.deleteMatch(match)
                    print("✅ Successfully deleted match from CloudKit: \(match.team1Player1.name) vs \(match.team2Player1.name)")
                } catch {
                    // If CloudKit deletion fails, add the match back
                    await MainActor.run {
                        dataManager.matches.append(match)
                        dataManager.errorMessage = "Failed to delete match: \(error.localizedDescription)"
                    }
                }
            }
        }

        // Recalculate statistics after local deletion
        dataManager.recalculateAllPlayerStatistics()
    }
}

struct MatchRowView: View {
    let match: Match

    private var isPhone: Bool {
        UIDevice.current.userInterfaceIdiom == .phone
    }

    var body: some View {
        if isPhone {
            // Compact iPhone layout
            VStack(alignment: .leading, spacing: 6) {
                // Top row: Match description (truncated for iPhone)
                HStack {
                    Text(compactMatchDescription)
                        .font(.headline)
                        .lineLimit(1)
                        .minimumScaleFactor(0.8)

                    Spacer()

                    // Match score/status on the right
                    matchStatusView
                }

                // Bottom row: Compact metadata
                HStack {
                    // Match type icon + short name
                    HStack(spacing: 4) {
                        Image(systemName: match.type == .singles ? "person" : "person.2")
                            .font(.caption)
                            .foregroundColor(.blue)
                        Text(compactMatchType)
                            .font(.caption)
                            .foregroundColor(.blue)
                    }

                    // Status indicator
                    HStack(spacing: 4) {
                        Image(systemName: statusIcon(match.status))
                            .font(.caption)
                            .foregroundColor(statusColor(match.status))
                        Text(compactStatusName)
                            .font(.caption)
                            .foregroundColor(statusColor(match.status))
                    }

                    Spacer()

                    // Date
                    Text(displayDate, formatter: shortDateFormatter)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding(.vertical, 6)
        } else {
            // Original iPad layout
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    // Match description
                    Text(match.description)
                        .font(.headline)
                        .lineLimit(1)

                    // Match metadata with fixed column widths
                    HStack(spacing: 0) {
                        // Match type column
                        HStack(spacing: 4) {
                            Image(systemName: match.type == .singles ? "person" : "person.2")
                                .font(.caption)
                                .foregroundColor(.blue)
                            Text(match.type.displayName)
                                .font(.caption)
                                .foregroundColor(.blue)
                                .lineLimit(1)
                                .minimumScaleFactor(0.8)
                        }
                        .frame(width: 100, alignment: .leading)

                        // Separator
                        Text("-")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .frame(width: 20, alignment: .center)

                        // Match status column
                        HStack(spacing: 4) {
                            Image(systemName: statusIcon(match.status))
                                .font(.caption)
                                .foregroundColor(statusColor(match.status))
                            Text(match.status.displayName)
                                .font(.caption)
                                .foregroundColor(statusColor(match.status))
                        }
                        .frame(width: 80, alignment: .leading)

                        // Separator
                        Text("-")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .frame(width: 20, alignment: .center)

                        // Date column
                        Text(displayDate, formatter: dateFormatter)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .frame(width: 70, alignment: .leading)

                        Spacer()
                    }
                }

                Spacer()

                matchStatusView
            }
            .padding(.vertical, 4)
        }
    }

    // MARK: - Computed Properties

    private var compactMatchDescription: String {
        if match.type == .mixMatch {
            // For Mix & Match, show only first 2 players + "& 2 more"
            let players = match.allPlayers
            if players.count >= 2 {
                return "\(players[0].name) & \(players[1].name) + 2"
            }
            return "Mix & Match"
        }

        // For Singles and Doubles, use shorter names if needed
        let team1Name = match.type == .doubles ?
            "\(shortName(match.team1Player1.name)) & \(shortName(match.team1Player2?.name ?? ""))" :
            shortName(match.team1Player1.name)
        let team2Name = match.type == .doubles ?
            "\(shortName(match.team2Player1.name)) & \(shortName(match.team2Player2?.name ?? ""))" :
            shortName(match.team2Player1.name)
        return "\(team1Name) vs \(team2Name)"
    }

    private var compactMatchType: String {
        switch match.type {
        case .singles: return "1v1"
        case .doubles: return "2v2"
        case .mixMatch: return "Mix"
        }
    }

    private var compactStatusName: String {
        switch match.status {
        case .scheduled: return "Planned"
        case .inProgress: return "Live"
        case .completed: return "Done"
        case .cancelled: return "Cancelled"
        }
    }

    private var displayDate: Date {
        return match.completedAt ?? match.createdAt
    }

    private var matchStatusView: some View {
        VStack(alignment: .trailing, spacing: 4) {
            if match.status == .completed {
                if match.type == .mixMatch {
                    // For Mix & Match: status only
                    Text(isPhone ? "Done" : "Completed")
                        .font(.title3)
                        .fontWeight(.bold)
                        .foregroundColor(.green)
                } else {
                    // For Singles and Doubles: traditional team score
                    Text("\(match.team1GamesWon) - \(match.team2GamesWon)")
                        .font(.title3)
                        .fontWeight(.bold)

                    // Only show winner name on iPad, not on iPhone
                    if !isPhone, let winner = match.winner {
                        Text(getWinnerText(for: match, winner: winner))
                            .font(.caption)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color.green)
                            .foregroundColor(.white)
                            .cornerRadius(4)
                            .lineLimit(1)
                    }
                }
            } else if match.status == .inProgress {
                if match.type == .mixMatch {
                    // For Mix & Match: status only
                    Text(isPhone ? "Playing..." : "In Progress...")
                        .font(.title3)
                        .fontWeight(.bold)
                        .foregroundColor(.orange)
                } else {
                    // For Singles and Doubles: traditional team score
                    Text("\(match.team1GamesWon) - \(match.team2GamesWon)")
                        .font(.subheadline)
                        .foregroundColor(.orange)

                    Text(isPhone ? "Playing..." : "In Progress...")
                        .font(.caption)
                        .foregroundColor(.orange)
                }
            } else {
                Text("Best of \(match.bestOfGames)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }

    // Helper function to shorten names for iPhone display
    private func shortName(_ name: String) -> String {
        if name.count > 8 {
            return String(name.prefix(8)) + "."
        }
        return name
    }
    
    private func statusIcon(_ status: MatchStatus) -> String {
        switch status {
        case .scheduled: return "calendar"
        case .inProgress: return "play.circle"
        case .completed: return "checkmark.circle"
        case .cancelled: return "xmark.circle"
        }
    }
    
    private func statusColor(_ status: MatchStatus) -> Color {
        switch status {
        case .scheduled: return .blue
        case .inProgress: return .orange
        case .completed: return .green
        case .cancelled: return .red
        }
    }
    
    private var dateFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .none
        return formatter
    }

    private var shortDateFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateFormat = "dd/MM"
        return formatter
    }

    private func getWinnerText(for match: Match, winner: Team) -> String {
        if match.type == .mixMatch {
            // Voor Mix & Match: toon de daadwerkelijke winnaar(s)
            let winners = match.mixMatchWinners
            if winners.count == 1 {
                return winners[0].name
            } else if winners.count > 1 {
                return winners.map { $0.name }.joined(separator: " & ")
            } else {
                return "No winner"
            }
        } else if match.type == .singles {
            return winner == .team1 ? match.team1Player1.name : match.team2Player1.name
        } else {
            return winner == .team1 ? "Team 1" : "Team 2"
        }
    }
}



#Preview {
    NavigationView {
        MatchListView(showingSettings: .constant(false))
    }
    .environmentObject(DataManager())
}
