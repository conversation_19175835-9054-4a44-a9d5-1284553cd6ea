import SwiftUI

struct PlayerListView: View {
    @EnvironmentObject var dataManager: DataManager
    @State private var showingAddPlayer = false
    @Binding var showingSettings: Bool

    @State private var sortOption: PlayerSortOption = .elo

    enum PlayerSortOption: String, CaseIterable {
        case elo = "ELO"
        case matches = "Matches"
        case matchWinPercentage = "Match %"
        case gameWinPercentage = "Game %"
    }

    var filteredAndSortedPlayers: [Player] {
        print("🎯 PlayerListView: Computing filteredAndSortedPlayers with \(dataManager.players.count) players")

        let sorted = dataManager.players.sorted { player1, player2 in
            switch sortOption {
            case .elo:
                return player1.eloRating > player2.eloRating
            case .matches:
                return player1.matchesPlayed > player2.matchesPlayed
            case .matchWinPercentage:
                return player1.matchWinPercentage > player2.matchWinPercentage
            case .gameWinPercentage:
                return player1.gameWinPercentage > player2.gameWinPercentage
            }
        }

        print("🎯 PlayerListView: Returning \(sorted.count) filtered and sorted players")
        return sorted
    }

    var body: some View {
        VStack {
            // Sort Controls
            VStack(spacing: 12) {
                Picker("Sort by", selection: $sortOption) {
                    ForEach(PlayerSortOption.allCases, id: \.self) { option in
                        Text(option.rawValue).tag(option)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
            }
            .padding(.horizontal)

            // Players List
            if filteredAndSortedPlayers.isEmpty {
                let _ = print("🎯 PlayerListView: Showing empty state - dataManager.players.count = \(dataManager.players.count)")
                EmptyStateView(
                    title: "No players",
                    subtitle: "Add your first player to get started",
                    systemImage: "person.2"
                )
            } else {
                let _ = print("🎯 PlayerListView: Showing \(filteredAndSortedPlayers.count) players")
                ScrollView {
                    VStack(alignment: .leading, spacing: 12) {
                        ForEach(Array(filteredAndSortedPlayers.enumerated()), id: \.element.id) { index, player in
                            NavigationLink(destination: PlayerDetailView(player: player)) {
                                PlayerRowView(
                                    player: player,
                                    position: index + 1,
                                    showConfetti: index == 0 && sortOption == .elo
                                )
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                    }
                    .padding(.vertical)
                }
                .refreshable {
                    await dataManager.loadData()
                }
            }
        }
        .navigationTitle("Players")
        .navigationBarTitleDisplayMode(.large)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: { showingAddPlayer = true }) {
                    Image(systemName: "plus")
                }
            }

            ToolbarItem(placement: .navigationBarLeading) {
                Button {
                    showingSettings = true
                } label: {
                    Image(systemName: "gear")
                }
            }
        }
        .sheet(isPresented: $showingAddPlayer) {
            AddPlayerView()
        }
        .onAppear {
            print("🎯 PlayerListView: onAppear - dataManager.players.count = \(dataManager.players.count)")
        }
    }


}

struct PlayerRowView: View {
    let player: Player
    let position: Int
    let showConfetti: Bool

    init(player: Player, position: Int, showConfetti: Bool = false) {
        self.player = player
        self.position = position
        self.showConfetti = showConfetti
    }

    private var rankColor: Color {
        switch position {
        case 1: return .yellow
        case 2: return .gray
        case 3: return .orange
        default: return .primary
        }
    }

    var body: some View {
        HStack {
            // Rank
            Text("\(position)")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(rankColor)
                .frame(width: 30)

            // Player info
            HStack {
                Circle()
                    .fill(Color.blue.gradient)
                    .frame(width: 40, height: 40)
                    .overlay(
                        Text(String(player.name.prefix(1)).uppercased())
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                    )

                VStack(alignment: .leading, spacing: 2) {
                    Text(player.name)
                        .font(.subheadline)
                        .fontWeight(.medium)

                    Text("\(player.matchesPlayed) matches")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                Text("\(Int(player.eloRating))")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(rankColor)
            }
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        .background(position <= 3 ? rankColor.opacity(0.1) : Color.clear)
        .cornerRadius(8)
        .padding(.horizontal)
        .overlay(
            // Confetti effect for #1 player
            Group {
                if showConfetti {
                    SimpleConfettiView()
                        .allowsHitTesting(false)
                        .clipped()
                }
            }
        )
    }


}



struct EmptyStateView: View {
    let title: String
    let subtitle: String
    let systemImage: String
    
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: systemImage)
                .font(.system(size: 48))
                .foregroundColor(.secondary)
            
            VStack(spacing: 8) {
                Text(title)
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Text(subtitle)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}



#Preview {
    PlayerListView(showingSettings: .constant(false))
        .environmentObject(DataManager())
}
